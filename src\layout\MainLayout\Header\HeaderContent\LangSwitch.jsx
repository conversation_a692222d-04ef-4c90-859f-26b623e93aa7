import { useRef, useState } from "react";
import React from "react";
// material-ui
import { useTheme } from "@mui/material/styles";
import {
  Avatar,
  Badge,
  Box,
  MenuItem,
  Select,
  Stack,
  ClickAwayListener,
  Divider,
  IconButton,
  List,
  ListItemButton,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Paper,
  Popper,
  Typography,
  useMediaQuery,
} from "@mui/material";
// lang
import { useTranslation } from "react-i18next";
// project import
import MainCard from "@/components/MainCard";
import Transitions from "@/components/@extended/Transitions";

// assets
import {
  GlobalOutlined,
  CloseOutlined,
  GiftOutlined,
  MessageOutlined,
  SettingOutlined,
} from "@ant-design/icons";
import { getStoreLang, setStoreLang } from "@/utils/langUtils";
import i18n from "i18next";

// sx styles
const avatarSX = {
  width: 36,
  height: 36,
  fontSize: "1rem",
};

const actionSX = {
  mt: "6px",
  ml: 1,
  top: "auto",
  right: "auto",
  alignSelf: "flex-start",

  transform: "none",
};

const LangSwitch = () => {
  const { i18n } = useTranslation();
  const theme = useTheme();
  const matchesXs = useMediaQuery(theme.breakpoints.down("md"));

  const anchorRef = useRef(null);
  const [open, setOpen] = useState(false);
  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };

  const iconBackColorOpen = "grey.300";
  const iconBackColor = "grey.100";

  const switchLang = useCallback(
    (lang) => {
      i18n.changeLanguage(lang);
      setStoreLang(lang);
      setOpen(false);
    },
    [i18n]
  );

  useEffect(() => {
    const handleLanguageChange = () => {
      window.history.pushState(
        { language: i18n.language },
        "",
        window.location.href
      );
    };

    i18n.on("languageChanged", handleLanguageChange);

    return () => {
      i18n.off("languageChanged", handleLanguageChange);
    };
  }, [i18n]);

  return (
    <Box sx={{ flexShrink: 0, ml: 0.75 }}>
      <IconButton
        disableRipple
        color="secondary"
        sx={{
          color: "text.primary",
          bgcolor: open ? iconBackColorOpen : iconBackColor,
        }}
        aria-label="open profile"
        ref={anchorRef}
        aria-controls={open ? "profile-grow" : undefined}
        aria-haspopup="true"
        onClick={handleToggle}>
        <GlobalOutlined />
      </IconButton>
      <Popper
        placement={matchesXs ? "bottom" : "bottom-end"}
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
        popperOptions={{
          modifiers: [
            {
              name: "offset",
              options: {
                offset: [matchesXs ? -5 : 0, 9],
              },
            },
          ],
        }}>
        {({ TransitionProps }) => (
          <Transitions type="fade" in={open} {...TransitionProps}>
            <Paper
              sx={{
                boxShadow: theme.customShadows.z1,
                minWidth: 140,
                border: "1px solid",
                borderColor: theme.palette.divider,
                borderRadius: "12px",
              }}>
              <ClickAwayListener onClickAway={handleClose}>
                <>
                  <List
                    component="nav"
                    sx={{
                      p: 0,
                      "& .MuiListItemIcon-root": {
                        minWidth: 32,
                      },
                    }}>
                    <ListItemButton
                      sx={{ borderRadius: `12px` }}
                      selected={i18n.language === "zh"}
                      onClick={() => {
                        switchLang("zh");
                      }}>
                      <ListItemText
                        primary={<Typography variant="h5">中文</Typography>}
                      />
                    </ListItemButton>
                    <ListItemButton
                      sx={{ borderRadius: `12px` }}
                      selected={i18n.language === "en"}
                      onClick={() => {
                        switchLang("en");
                      }}>
                      <ListItemText
                        primary={<Typography variant="h5">English</Typography>}
                      />
                    </ListItemButton>
                    <ListItemButton
                      sx={{ borderRadius: `12px` }}
                      selected={i18n.language === "es"}
                      onClick={() => {
                        switchLang("es");
                      }}>
                      <ListItemText
                        primary={<Typography variant="h5">Español</Typography>}
                      />
                    </ListItemButton>
                    <ListItemButton
                      sx={{ borderRadius: `12px` }}
                      selected={i18n.language === "jp"}
                      onClick={() => {
                        switchLang("jp");
                      }}>
                      <ListItemText
                        primary={<Typography variant="h5">日本語</Typography>}
                      />
                    </ListItemButton>
                    <ListItemButton
                      sx={{ borderRadius: `12px` }}
                      selected={i18n.language === "pt"}
                      onClick={() => {
                        switchLang("pt");
                      }}>
                      <ListItemText
                        primary={
                          <Typography variant="h5">Português</Typography>
                        }
                      />
                    </ListItemButton>
                  </List>
                </>
              </ClickAwayListener>
            </Paper>
          </Transitions>
        )}
      </Popper>
    </Box>
  );
};

export default LangSwitch;
