import React from "react";
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import { useEffect, useMemo, useState, useRef, forwardRef } from "react";
import MaterialReactTable from "material-react-table";
import {
  <PERSON><PERSON>,
  <PERSON>ack,
  Typography,
  Link,
  IconButton,
  Tooltip,
  Card,
  Grid,
  CardMedia,
  Pagination,
  Skeleton,
  TextField,
} from "@mui/material";
import DictTag from "@/components/DictTag";
import SyncIcon from "@mui/icons-material/Sync";
import { getTreeSelect } from "@/service/api/materialGroup";
import UploadMaterial from "./UploadMaterial";
import Preview from "./Preview";
import { useConfirm } from "@/components/zkconfirm";
import { useFormik } from "formik";
import { useLocation } from "react-router-dom";
// api
import { listByPage, del, audit } from "@/service/api/material";
import Treeselect from "@/components/zktreeselect";
import { toast } from "react-toastify";
import { tableI18n } from "@/utils/tableLang";
import "./table.less";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import FilterColumn from "@/components/tableColumn";
import ModeEditOutlineIcon from "@mui/icons-material/ModeEditOutline";

import AddAPhotoIcon from "@mui/icons-material/AddAPhoto";
import VisibilityIcon from "@mui/icons-material/Visibility";
// i18n
import { useTranslation } from "react-i18next";
import MainCard from "@/components/MainCard";
import { removeEmpty } from "@/utils/StringUtils";
import { getMerchantSelect } from "@/service/api/merchant";
import ZKSelect from "@/components/ZKSelect";
import UploadImage from "./uploadImage";
import AuthButton from "@/components/AuthButton";
import AddNewLayout from "./AddNewLayout";
import PreViewDia from "../../../neweditor/PreViewDia";
import { useNavigate } from "react-router-dom";

import {
  directions,
  fileTypes,
  fileResourceStatus,
  merchantTypes,
  materialAudit,
} from "@/dict/commonDict";
import GroupDialog from "@/pages/program/group/index";
import { useBoolean } from "ahooks";
import AiHelper from "./AiHelper";
import { getPrincipaList } from "@/service/api/L3Sevice.js";
const Example = (props) => {
  const { t } = useTranslation();
  const confirm = useConfirm();
  const location = useLocation();
  const [rowSelection, setRowSelection] = useState([]); //行选中状态
  const [open, setOpen] = useState(false);
  const [isMobileTemplate, setIsMobileTemplate] = useState(false);
  const [openLayout, setOpenLayout] = useState(false);
  const treeSelectRef = React.useRef(null);
  const navigate = useNavigate();
  const [columnVisibility, setColumnVisibility] = useState({});
  const key = window.location.hash + "/columnVisibility";
  useEffect(() => {
    const savedVisibility = localStorage.getItem(key);
    if (savedVisibility) {
      setColumnVisibility(JSON.parse(savedVisibility));
    } else {
      // 初始化为所有列都显示
      const initialVisibility = columns.reduce((acc, col) => {
        acc[col.accessorKey] = true;
        return acc;
      }, {});
      setColumnVisibility(initialVisibility);
    }
  }, []);

  const [aiOpen, { setTrue: handleOpenAi, setFalse: handleCloseAi }] =
    useBoolean(false);

  // 删除
  const handleDel = (values) => {
    const ids = [values.original.id];
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("ips.ips_material_del", { id: values.original.name }),
    }).then(() => {
      del(ids)
        .then((res) => {
          toast.success(res.message);
          //重置复选框
          setRowSelection([]);
          getTableData();
        })
        .catch((res) => {});
    });
  };
  //批量删除
  const batchDel = (values) => {
    let selectDatas = values.getSelectedRowModel().flatRows;
    if (selectDatas.length == 0) {
      toast.error(t("common.common_check_data"));
      return;
    }
    let params = [];
    let names = [];
    for (let data of selectDatas) {
      let ori = data.original;
      names.push(ori.name);
      params.push(ori.id);
    }
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("ips.ips_material_del", { id: names }),
    }).then(() => {
      del(params)
        .then((res) => {
          toast.success(res.message);
          //重置复选框
          values.resetRowSelection(null);
          // getTableData();
          bathDeleteRefreshTable();
        })
        .catch((res) => {});
    });
  };
  // 素材审核
  const handleAudit = (values) => {
    const ids = [values.original.id];
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("ips.ips_material_audit"),
    }).then(() => {
      audit(ids)
        .then((res) => {
          toast.success(res.message);
          //重置复选框
          setRowSelection([]);
          getTableData();
        })
        .catch((res) => {});
    });
  };

  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  // 排序参数
  const [sorting, setSorting] = useState([]);

  // 预览url
  const [previewUrl, setPreviewUrl] = useState("");
  //预览文件类型
  const [previewType, setPreviewType] = useState("");
  //时间参数
  const dataRangeRef = useRef([new Date(), new Date()]);
  const [layoutPre, setLayoutPre] = useState(false);
  const [layoutJson, setLayoutJson] = useState({});
  const closeLayoutPreview = () => {
    setLayoutPre(false);
  };
  // 查询参数
  const requestParams = useRef(null);
  //广告商数据
  const [merchantList, setMerchantList] = useState([]);

  // 构建参数
  const buildParams = () => {
    let tab = props.tabValues;
    if (tab == "all") {
      tab = "";
    }
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      orderProp: "create_time",
      orderBy: "desc",
      type: tab,
      showAudited: false,
      ...requestParams.current,
    };
    return params;
  };
  //获取零售商列表

  useEffect(() => {
    getPrincipaList().then((res) => {
      setMerchantList(res?.data || []);
    });
  }, []);

  const handlePreview = async (values) => {
    if (
      values.original.type === "layout" ||
      values.original.type === "mobile"
    ) {
      try {
        let json = JSON.parse(values.original.layoutJson);
        if (json) {
          setLayoutJson(json);
          setLayoutPre(true);
        } else {
          setLayoutPre(false);
        }
      } catch (error) {
        console.log(error);
      }
    } else {
      setPreviewUrl(values.original.downloadUrl);
      setPreviewType(values.original.type);
      preview.current.handleOpen();
    }
  };
  const previewClick = (values) => {
    if (values.type === "layout" || values?.type === "mobile") {
      try {
        let json = JSON.parse(values.layoutJson);
        if (json) {
          setLayoutJson(json);
          setLayoutPre(true);
        } else {
          setLayoutPre(false);
        }
      } catch (error) {
        console.log(error);
      }
    } else {
      setPreviewUrl(values.downloadUrl);
      setPreviewType(values.type);
      preview.current.handleOpen();
    }
  };
  const deleteClick = (values) => {
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("ips.ips_material_del", { id: values.name }),
    }).then(() => {
      del(values.id)
        .then((res) => {
          toast.success(res.message);
          cardRef.current.getList();
          // 列表刷新
          getTableData();
        })
        .catch((res) => {});
    });
  };
  const bathDeleteRefreshTable = () => {
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    });
    // 列表刷新
    getTableData();
  };

  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    // // 开启加载
    // setIsLoading(true);
    // setIsRefetching(true);
    await listByPage(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    // 发请求
    getTableData();
    setRowSelection([]);
  }, [pagination.pageIndex, pagination.pageSize, location]);
  // 列字段
  const columns = useMemo(() => {
    const columnMain = [
      {
        accessorKey: "name", //access nested data with dot notation
        header: t("common.common_content_name"),
        //列头点击相关事件关闭
        enableColumnActions: false,
        size: 150,
        Cell: ({ row }) => {
          return (
            <>
              {row?.original?.name === null ? (
                <Typography>-</Typography>
              ) : (
                <Tooltip title={row?.original?.name} placement="top">
                  <Typography
                    sx={{
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    }}>
                    {row?.original?.name}
                  </Typography>
                </Tooltip>
              )}
            </>
          );
        },
      },
      {
        accessorKey: "direction",
        header: t("common.common_direction"),
        enableColumnActions: false,
        size: 120,
        Cell: ({ row }) => {
          return (
            <>
              {row?.original?.direction === null ? (
                <Typography>/</Typography>
              ) : (
                <DictTag
                  dicts={directions}
                  value={row.original.direction}
                  fieldName={{ value: "value", title: "label", color: "color" }}
                />
              )}
            </>
          );
        },
      },
      {
        accessorKey: "departmentName",
        header: t("ips.ips_merchant_name"),
        enableColumnActions: false,
        size: 160,
        Cell: ({ row }) => {
          return (
            <>
              {row?.original?.departmentName === null ? (
                <Typography>-</Typography>
              ) : (
                <Tooltip title={row?.original?.departmentName} placement="top">
                  <Typography
                    sx={{
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    }}>
                    {row?.original?.departmentName}
                  </Typography>
                </Tooltip>
              )}
            </>
          );
        },
      },
      {
        accessorKey: "groupName",
        header: t("common.common_material_category_table_column_name"),
        enableColumnActions: false,
        Cell: ({ row }) => {
          return (
            <>
              {row?.original?.groupName === null ? (
                <Typography>-</Typography>
              ) : (
                <Tooltip title={row?.original?.groupName} placement="top">
                  <Typography
                    sx={{
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    }}>
                    {row?.original?.groupName}
                  </Typography>
                </Tooltip>
              )}
            </>
          );
        },
      },

      {
        accessorKey: "resolution",
        enableColumnActions: false,
        header: t("common.common_resolution_ratio"),
        size: 120,
      },
      {
        accessorKey: "type",
        enableColumnActions: false,
        size: 80,
        header: t("ips.ips_type"),
        Cell: ({ cell, row }) => {
          return <DictTag dicts={fileTypes} value={row.original.type} />;
        },
      },
      {
        accessorKey: "format", //normal accessorKey
        enableColumnActions: false,
        size: 100,
        header: t("common.common_format"),
        Cell: ({ cell, row }) => {
          return (
            <Typography>
              {row.original.format === null ? "/" : row.original.format}
            </Typography>
          );
        },
      },
      {
        accessorKey: "fileSize", //normal accessorKey
        enableColumnActions: false,
        //关闭列属性过滤
        enableColumnFilter: false,
        minSize: 80,
        // maxSize: 80,
        header: t("common.common_size"),
        // size: '100'
        Cell: ({ cell, row }) => {
          return (
            <Typography>
              {row.original.fileSize === null ? "/" : row.original.fileSize}
            </Typography>
          );
        },
      },
      {
        accessorKey: "status",
        enableColumnActions: false,
        filterVariant: "select",
        filterSelectOptions: [
          { text: t("dictData.dict_convert"), value: "0" },
          { text: t("dictData.dict_convert_success"), value: "2" },
          { text: t("dictData.dict_convert_failed"), value: "4" },
          { text: t("dictData.dict_not_complete"), value: "5" },
        ],
        header: t("common.common_status"),
        Cell: ({ cell, row }) => {
          return (
            <>
              {row?.original?.type === "layout" ||
              row?.original?.type === "mobile" ? (
                <Typography>/</Typography>
              ) : (
                <DictTag
                  value={
                    row.original.type == "layout" ||
                    row?.original?.type === "mobile"
                      ? "/"
                      : row.original.status
                  }
                  dicts={fileResourceStatus}
                />
              )}
            </>
          );
        },
      },
      // {
      //   accessorKey: "createdAt",
      //   enableColumnActions: false,
      //   Cell: ({ cell }) => cell.getValue()?.toLocaleString(), //render Date as a string
      //   sortingFn: "datetime",
      //   header: t("common.common_createdTime"),
      //   size: "180",
      // },
    ];
    const auditColumn = {
      accessorKey: "audit",
      header: t("menu.button_material_examine"),
      size: 150,
      enableColumnActions: false,
      Cell: ({ cell, row }) => {
        return (
          <DictTag
            dicts={materialAudit}
            fieldName={{ title: "label" }}
            value={row.original.audit}
          />
        );
      },
    };
    if (localStorage.getItem("showAudit") === "Y") {
      const tempColumns = [...columnMain];
      tempColumns.splice(columnMain.length - 2, 0, auditColumn);
      return tempColumns;
    } else {
      return columnMain;
    }
  }, [localStorage.getItem("showAudit")]);
  const upload = React.useRef(null);
  const preview = React.useRef(null);
  const cardRef = React.useRef(null);
  const [groups, setGroups] = useState([]);
  //上传封面图
  const uploadImageRef = useRef(null);

  const handleUpload = (id) => {
    uploadImageRef.current.handleOpen(id);
  };

  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      name: "",
      advertiserId: "",
      direction: "",
      resolution: "",
      audit: "",
      groupId: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        // 拷贝value值
        const tempValue = { ...values };
        await removeEmpty(tempValue);
        requestParams.current = tempValue;
        setPagination({
          pageIndex: 0,
          pageSize: 10,
        });
        getTableData();

        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });
  // 重置查询
  const resetQuery = () => {
    handleClearGroup();
    // 重置表单
    queryFormik.resetForm();
    requestParams.current = null;
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    });
    getTableData();
  };
  // 关闭
  const handlClseCancel = () => {
    setOpen(false);
    getTableData();
  };

  const handleNewLayoutDiaClo = (isReload) => {
    setOpenLayout(false);
    setIsMobileTemplate(false);
    if (isReload) {
      getTableData();
    }
  };

  const handleEdit = (info) => {
    let value = "";
    if (info.original) {
      value = info.original;
    } else {
      value = info;
    }
    //
    // let value = info.original;
    if (value.type === "mobile") {
      sessionStorage.setItem("isAdd", false);
      sessionStorage.setItem("isMobile", true);
      sessionStorage.setItem("editValue", JSON.stringify(value));
    } else {
      sessionStorage.setItem("isAdd", false);
      sessionStorage.setItem("isMobile", false);
      sessionStorage.setItem("editValue", JSON.stringify(value));
    }
    sessionStorage.setItem("templateLayout", false);
    navigate("/neweditor");
  };
  useEffect(() => {
    getOption();
  }, []);
  const getOption = () => {
    getTreeSelect({ departmentId: queryFormik.values.advertiserId }).then(
      (res) => {
        setGroups(res?.data);
      }
    );
  };
  const handleClearGroup = () => {
    treeSelectRef?.current?.clear();
    queryFormik.setFieldValue("groupId", "");
  };

  return (
    <>
      {props.type === "list" ? (
        <>
          <MainCard style={{ marginBottom: "10px" }}>
            <form noValidate onSubmit={queryFormik.handleSubmit}>
              <Grid
                container
                direction="row"
                justifyContent="flex-start"
                alignItems="center"
                spacing={1}>
                <Grid item md={6} lg={1.5} xs={12}>
                  <TextField
                    label={t("common.common_content_name")}
                    value={queryFormik.values.name}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                    size="small"
                    type="text"
                    name="name"
                    fullWidth
                    placeholder={t("common.common_please_input")}
                  />
                </Grid>
                <Grid item md={6} lg={2} xs={12}>
                  <Treeselect
                    ref={treeSelectRef}
                    size={"small"}
                    data={groups}
                    isClear={true}
                    optionValue="id"
                    optionLabel="name"
                    placeholder={t("common.common_material_category_please")}
                    onChange={(valuas) => {
                      queryFormik.values.groupId = valuas.id;
                    }}
                    onClear={handleClearGroup}
                    disableParent={true}
                  />
                </Grid>

                <Grid item md={6} lg={2} xs={12}>
                  <ZKSelect
                    id="advertiserId"
                    size="small"
                    name="advertiserId"
                    value={queryFormik.values.advertiserId}
                    options={merchantList}
                    // labelOptions={{
                    //   label: "label",
                    //   value: "value",
                    // }}
                    onClear={() => {
                      queryFormik.setFieldValue("advertiserId", "");
                    }}
                    onBlur={queryFormik.handleBlur}
                    onChange={queryFormik.handleChange}
                    type="text"
                    menuWidth={200}
                    placeholder={t("ips.ips_select_merchant")}
                  />
                </Grid>
                <Grid item md={6} lg={1.5} xs={12}>
                  <TextField
                    label={t("ips.ips_resolution")}
                    value={queryFormik.values.resolution}
                    onChange={queryFormik.handleChange}
                    onBlur={queryFormik.handleBlur}
                    size="small"
                    type="text"
                    name="resolution"
                    fullWidth
                    placeholder={t("common.common_input_resolution_ratio")}
                  />
                </Grid>
                <Grid item md={6} lg={1.5} xs={12}>
                  <ZKSelect
                    size="small"
                    id="direction"
                    name="direction"
                    value={queryFormik.values.direction}
                    options={directions}
                    onClear={() => {
                      queryFormik.setFieldValue("direction", undefined);
                    }}
                    onBlur={queryFormik.handleBlur}
                    onChange={queryFormik.handleChange}
                    type="text"
                    menuWidth={260}
                    placeholder={t("ips.ips_please_select_direction")}
                  />
                </Grid>
                {localStorage.getItem("showAudit") === "Y" && (
                  <Grid item md={6} lg={1.5} xs={12}>
                    <ZKSelect
                      label={t("menu.button_material_examine")}
                      size="small"
                      id="audit"
                      name="audit"
                      value={queryFormik.values.audit}
                      options={materialAudit}
                      onClear={() => {
                        queryFormik.setFieldValue("audit", undefined);
                      }}
                      onBlur={queryFormik.handleBlur}
                      onChange={queryFormik.handleChange}
                      type="text"
                      menuWidth={200}
                      placeholder={t("ips.ips_select_audit_status")}
                    />
                  </Grid>
                )}

                <Grid item md={6} lg={2} xs={12}>
                  <Stack
                    direction="row"
                    justifyContent="flex-start"
                    alignItems="flex-start"
                    spacing={2}>
                    <Button
                      disableElevation
                      type="submit"
                      variant="contained"
                      size="small">
                      {t("common.common_table_query")}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={resetQuery}
                      color="info"
                      size="small">
                      {t("common.common_op_reset")}
                    </Button>
                  </Stack>
                </Grid>
              </Grid>
            </form>
          </MainCard>
          <MaterialReactTable
            // table 状态
            state={{
              // 加载状态
              isLoading,
              // 分页参数
              pagination,
              columnVisibility: columnVisibility,
              // 重新拉取
              showProgressBars: isRefetching,
              showAlertBanner: isError,
              columnPinning: { right: ["mrt-row-actions"] },
              sorting,
              rowSelection,
            }}
            renderToolbarInternalActions={({ table }) => (
              <>
                <GroupDialog merchantList={merchantList} />
                <Tooltip
                  arrow
                  placement="top"
                  title={t("common.common_op_refresh")}>
                  <IconButton aria-label="sync" onClick={getTableData}>
                    <SyncIcon />
                  </IconButton>
                </Tooltip>
                <FilterColumn
                  columns={table.getAllLeafColumns()}
                  columnVisibility={table.getState().columnVisibility}
                  table={table}
                />
              </>
            )}
            onColumnVisibilityChange={setColumnVisibility}
            // 解决列太多宽度太长问题
            enableColumnResizing
            // enablePinning
            // 初始化状态
            initialState={{ columnVisibility: { createTime: false } }}
            muiToolbarAlertBannerProps={
              isError
                ? {
                    color: "error",
                    children: t("table.loading_error"),
                  }
                : undefined
            }
            muiTablePaperProps={{
              elevation: 0,
              sx: {
                borderRadius: "5px",
                border: "1px solid #f0f0f0",
              },
            }}
            muiTableHeadRowProps={{
              sx: { backgroundColor: "#fafafa", boxShadow: "none" },
            }}
            //行选中
            onRowSelectionChange={setRowSelection}
            muiTableBodyRowProps={({ row }) => ({
              onClick: row.getToggleSelectedHandler(),
              sx: { cursor: "pointer" },
            })}
            // 开启多选
            enableRowSelection
            // 列数
            rowCount={rowCount}
            // 固定头部
            enableStickyHeader
            // 处理表格高度
            muiTableContainerProps={{ sx: { maxHeight: "620px" } }}
            // 设置背景颜色
            muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
            muiTableProps={{ sx: { backgroundColor: "white" } }}
            muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
            muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
            // 分页回调函数
            onPaginationChange={setPagination}
            // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
            manualFiltering
            manualPagination
            manualSorting
            // 排序
            onSortingChange={setSorting}
            // 开启分页
            enablePagination
            // 列定义
            columns={columns}
            // 数据
            data={data}
            // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
            localization={tableI18n}
            // 自定义表头按钮
            renderTopToolbarCustomActions={({ table }) => {
              const handleDeactivate = () => {
                table.getSelectedRowModel().flatRows.map((row) => {
                  alert("deactivating " + row.getValue("name"));
                });
              };

              const handleActivate = () => {
                table.getSelectedRowModel().flatRows.map((row) => {
                  alert("activating " + row.getValue("name"));
                });
              };

              const handleContact = () => {
                table.getSelectedRowModel().flatRows.map((row) => {
                  alert("contact " + row.getValue("name"));
                });
              };

              return (
                <div style={{ display: "flex", gap: "0.5rem" }}>
                  {props.tabValues !== "layout" &&
                    props.tabValues !== "mobile" && (
                      <AuthButton button="sd:resource:material:upload">
                        <Button
                          variant="contained"
                          onClick={() => {
                            setOpen(true);
                            // upload.current.handleOpen();
                          }}>
                          {t("ips.ips_advertising_resources_upload")}
                        </Button>
                      </AuthButton>
                    )}

                  {props.tabValues === "layout" || props.tabValues === "all" ? (
                    <AuthButton button="sd:resource:material:save:layout_material">
                      <Button
                        onClick={() => {
                          setIsMobileTemplate(false);
                          setOpenLayout(true);
                        }}
                        variant="contained">
                        {t("layout.add_new_layout")}
                      </Button>
                    </AuthButton>
                  ) : (
                    ""
                  )}

                  {props.tabValues === "mobile" && (
                    <AuthButton button="sd:resource:material:save:layout_material">
                      <Button
                        onClick={() => {
                          setIsMobileTemplate(true);
                          setOpenLayout(true);
                        }}
                        variant="contained">
                        {t("template.addMobileTemTitle")}
                      </Button>
                    </AuthButton>
                  )}

                  <AuthButton button="sd:resource:material:delete">
                    <Button
                      variant="contained"
                      color="secondary"
                      disabled={
                        !table.getIsSomeRowsSelected() &&
                        !table.getIsAllRowsSelected()
                      }
                      onClick={() => {
                        batchDel(table);
                      }}>
                      {t("common.common_op_batch_del")}
                    </Button>
                  </AuthButton>
                  <AuthButton button="sd:resource:material:upload">
                    <Button variant="outlined" onClick={handleOpenAi}>
                      {t("common.ai_text_to_picture")}
                    </Button>
                  </AuthButton>
                </div>
              );
            }}
            // 多选底部提示
            positionToolbarAlertBanner="none"
            // 开启action操作
            enableRowActions
            // action操作位置
            positionActionsColumn="last"
            //禁用列过滤
            // enableColumnFilters={false}
            displayColumnDefOptions={{
              "mrt-row-actions": {
                header: t("common.common_relatedOp"), //change header text
                size: 250, //make actions column wider
                overflowX: "auto",
              },
            }}
            renderRowActions={({ cell, row, table }) => (
              <Stack direction="row" spacing={2} alignItems="center">
                {row.original.status == "2" && (
                  <Link
                    component="button"
                    underline="none"
                    onClick={() => handlePreview(row)}>
                    {t("common.common_op_preview")}
                  </Link>
                )}
                {localStorage.getItem("showAudit") === "Y" &&
                  row.original.audit == "0" && (
                    <AuthButton button="sd:resource:material:audit">
                      <Link
                        component="button"
                        underline="none"
                        onClick={() => handleAudit(row)}>
                        {t("common.common_audit")}
                      </Link>
                    </AuthButton>
                  )}
                <Link
                  component="button"
                  underline="none"
                  onClick={() => handleAudit(row)}>
                  {t("common.common_audit")}
                </Link>
                <Link
                  component="button"
                  underline="none"
                  onClick={() => handleEdit(row)}
                  style={{ width: "30px" }}>
                  {t("common.common_op_edit")}
                </Link>
                {(row.original.type === "layout" ||
                  row.original.type === "mobile") &&
                  row.original.showEditOrDelBtn && (
                    <AuthButton button="sd:resource:material:update:layout_material">
                      <Link
                        component="button"
                        underline="none"
                        onClick={() => handleEdit(row)}
                        style={{ width: "30px" }}>
                        {t("common.common_op_edit")}
                      </Link>
                    </AuthButton>
                  )}

                <AuthButton button="sd:resource:material:delete">
                  <Link
                    component="button"
                    underline="none"
                    color="error"
                    onClick={() => handleDel(row)}
                    style={{ width: "30px" }}>
                    {t("common.common_op_del")}
                  </Link>
                </AuthButton>
              </Stack>
            )}
          />
        </>
      ) : (
        <CardMediaContent
          ref={cardRef}
          tabValues={props.tabValues}
          previewClick={previewClick}
          deleteClick={deleteClick}
          uploadClick={handleUpload}
          editClick={handleEdit}
        />
      )}

      <UploadMaterial
        ref={upload}
        open={open}
        onCancel={handlClseCancel}
        merchantList={merchantList}
      />

      <Preview ref={preview} url={previewUrl} type={previewType}></Preview>

      <PreViewDia
        showScale={true}
        visible={layoutPre}
        programData={layoutJson}
        onClose={closeLayoutPreview}></PreViewDia>

      <UploadImage
        ref={uploadImageRef}
        callback={() => {
          cardRef.current.getList();
        }}
      />

      <AddNewLayout
        isMobile={isMobileTemplate}
        open={openLayout}
        onCancel={handleNewLayoutDiaClo}
      />

      <AiHelper open={aiOpen} onClose={handleCloseAi} />
    </>
  );
};

// 卡片
const CardMediaContent = forwardRef((props, ref) => {
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(18);
  const [loading, setLoding] = useState(true);
  const [open, setOpen] = useState(false);
  const [isMobileTemplate, setIsMobileTemplate] = useState(false);
  const [openLayout, setOpenLayout] = useState(false);
  const upload = React.useRef(null);
  const { t } = useTranslation();
  const getList = () => {
    setLoding(true);
    listByPage({
      page: page,
      pageSize: pageSize,
      type: props.tabValues === "all" ? "" : props.tabValues,
      orderProp: "create_time",
      orderBy: "desc",
    })
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        setTotal(res.data.total);
        setLoding(false);
        // 设置总记录数
        // setRowCount(res.data.total);
        // setIsLoading(false);
        // setIsRefetching(false);
      })
      .catch((err) => {
        // setIsError(true);
        // setIsLoading(false);
        // setIsRefetching(false);
      });
  };
  React.useImperativeHandle(ref, () => ({
    getList,
  }));

  useEffect(() => {
    getList();
  }, [props.tabValues, page]);
  const getSrc = (item) => {
    if (
      item.type === "image" ||
      item.type === "layout" ||
      item.type === "mobile"
    ) {
      return item.downloadUrl;
    } else if (item.type === "media") {
      if (item.coverImage) {
        return item.coverImage;
      }
      return "https://armatura-minervaiot-dev-ap-southeast-1-private.s3.amazonaws.com/public/D0374F17B13643898E5836E3AED90924/applications/CloudMedia/eb0a59a4-6a4e-4fa5-8ba5-8e6187128477.png";
    } else {
      return "https://armatura-minervaiot-dev-ap-southeast-1-private.s3.amazonaws.com/public/D0374F17B13643898E5836E3AED90924/applications/CloudMedia/75d07d3a-9a50-41b6-87cd-c5d9d41be8fb.png";
    }
  };
  const onCancel = () => {
    setOpen(false);
    getList();
  };

  const handleNewLayoutDiaClo = (isReload) => {
    setOpenLayout(false);
    if (isReload) {
      getList();
    }
  };
  return (
    <>
      <MainCard style={{ marginBottom: "5px" }}>
        <div style={{ display: "flex", gap: "0.5rem" }}>
          {props.tabValues === "mobile" && (
            <AuthButton button="resource:materialLayout:save">
              <Button
                onClick={() => {
                  setIsMobileTemplate(true);
                  setOpenLayout(true);
                }}
                variant="contained">
                {t("template.addMobileTemTitle")}
              </Button>
            </AuthButton>
          )}

          {props.tabValues !== "layout" && props.tabValues !== "mobile" && (
            <AuthButton button="resource:material:upload">
              <Button
                variant="contained"
                onClick={() => {
                  setOpen(true);
                }}>
                {t("ips.ips_advertising_resources_upload")}
              </Button>
            </AuthButton>
          )}

          {props.tabValues === "layout" || props.tabValues === "all" ? (
            <AuthButton button="resource:materialLayout:save">
              <Button
                onClick={() => {
                  setIsMobileTemplate(false);
                  setOpenLayout(true);
                }}
                variant="contained">
                {t("layout.add_new_layout")}
              </Button>
            </AuthButton>
          ) : (
            ""
          )}
        </div>
      </MainCard>
      {loading ? (
        <Grid
          container
          direction="row"
          alignItems="flex-start"
          spacing={3}
          sx={{ marginBottom: "70px" }}>
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18].map(
            (item, index) => {
              return (
                <Grid item xs={12} sm={6} md={4} lg={2} key={index}>
                  <Skeleton
                    animation="wave"
                    variant="rectangular"
                    width="100%"
                    height={160}
                  />
                  <Skeleton
                    animation="wave"
                    variant="rectangular"
                    width="100%"
                    height={60}
                  />
                </Grid>
              );
            }
          )}
        </Grid>
      ) : (
        <>
          <Grid
            container
            direction="row"
            alignItems="flex-start"
            spacing={3}
            sx={{ marginBottom: "70px" }}>
            {data.map((item, index) => {
              return (
                <Grid item xs={12} sm={6} md={4} lg={2} key={index}>
                  <Card variant="outlined" elevation={6}>
                    <div className="box">
                      <CardMedia
                        sx={{
                          objectFit: "contain",
                        }}
                        component={"img"}
                        height="150"
                        src={getSrc(item)}
                        alt={t("common.common_Unable_load")}
                      />
                      <div className="ovrly"></div>
                      <div className="btn">
                        <IconButton
                          className="bt1"
                          onClick={() => {
                            props.previewClick(item);
                          }}>
                          <VisibilityIcon />
                        </IconButton>

                        {item.type === "media" && (
                          <AuthButton button="resource:material:upload">
                            <IconButton
                              className="bt1"
                              onClick={() => {
                                props.uploadClick(item.id);
                              }}>
                              <AddAPhotoIcon />
                            </IconButton>
                          </AuthButton>
                        )}
                        {
                          <>
                            {(item.type === "layout" ||
                              item.type === "mobile") && (
                              <AuthButton button="resource:materialLayout:edit">
                                <IconButton
                                  className="bt1"
                                  onClick={() => {
                                    // 执行删除操作
                                    props.editClick(item);
                                  }}>
                                  <ModeEditOutlineIcon />
                                </IconButton>
                              </AuthButton>
                            )}
                            <AuthButton button="resource:material:delete">
                              <IconButton
                                className="bt2"
                                onClick={() => {
                                  // 执行删除操作
                                  props.deleteClick(item);
                                }}>
                                <DeleteOutlineIcon />
                              </IconButton>
                            </AuthButton>
                          </>
                        }
                      </div>
                    </div>
                    {/* <CardContent> */}
                    <Tooltip title={item.name} placement="right">
                      <Typography
                        gutterBottom
                        variant="h6"
                        component="div"
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          padding: "5px 10px 5px 10px",
                        }}>
                        {item.name}
                      </Typography>
                    </Tooltip>
                    {/* </CardContent> */}
                  </Card>
                </Grid>
              );
            })}
          </Grid>
          <Grid
            container
            direction="row"
            justifyContent="flex-end"
            alignItems="center"
            spacing={10}
            sx={{
              marginTop: "10px",
              backgroundColor: "red",
              position: "fixed",
              bottom: 0,
              height: "50px",
              background: "white",
              right: 0,
              // borderTop: '1px solid black',
              boxShadow: "0 2px 12px 0 rgba(0,0,0,.1)",
            }}>
            <div style={{ marginRight: "10px" }}>
              {" "}
              {t("common.common_table_count", { total: total })}
            </div>
            <Pagination
              count={Math.ceil(total / pageSize)}
              variant="outlined"
              page={page}
              shape="rounded"
              // siblingCount={3}
              onChange={(event, page) => {
                setPage(page);
              }}
            />
          </Grid>
        </>
      )}
      {/* <UploadMaterial ref={upload} callback={getList} /> */}
      <UploadMaterial ref={upload} open={open} onCancel={onCancel} />
      <AddNewLayout
        isMobile={isMobileTemplate}
        open={openLayout}
        onCancel={handleNewLayoutDiaClo}
      />
    </>
  );
});
export default Example;
