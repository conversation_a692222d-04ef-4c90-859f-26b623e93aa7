/* eslint-disable no-undef */
import { useEffect, useState } from "react";
import { Grid, Card, Typography } from "@mui/material";
import pin_orange from "@/assets/images/icons/pin_orange.svg";
import pin_green from "@/assets/images/icons/pin_green.svg";
import pin_blue from "@/assets/images/icons/pin_blue.svg";
import { useTranslation } from "react-i18next";
import { loadBaiduMapAPI } from "@/utils/baiduMapLoader"; // 引入百度地图加载器

const BaiDuMap = (props) => {
  let center = props.center;
  let position = { lat: 24.618211693728895, lng: 118.06088011225243 };

  const [map, setMap] = useState(null);
  const [bmapApiLoaded, setBmapApiLoaded] = useState(false);

  const [dataList, setDataList] = useState([]);

  const [showType, setShowType] = useState(["1", "2", "3"]);

  const [showData, setShowData] = useState([]);

  const [markers, setMarkers] = useState([]);
  const [zoom, setZoom] = useState(1);

  const { t } = useTranslation();

  const getSvgIcon = (Leve = "1") => {
    let fillColor = "";
    if (Leve === "1") {
      fillColor = "rgb(86,166,59)";
    } else if (Leve === "2") {
      fillColor = "rgb(107,149,238)";
    } else if (Leve === "3") {
      fillColor = "rgb(234,143,64)";
    }

    // 确保BMap对象存在后再使用
    if (window.BMap) {
      let icons = new BMap.Symbol(
        "M0 0 C6.85580569 4.2076404 11.26833084 10.1340168 13.19140625 17.921875 C14.30442032 25.65439383 12.65614117 31.93420041 8.5625 38.5 C6.22259831 41.41280605 3.67502836 44.08167006 1.0625 46.75 C0.39863281 47.44351563 -0.26523437 48.13703125 -0.94921875 48.8515625 C-4.07317781 52.10646596 -7.2310006 55.32621998 -10.4375 58.5 C-13.82416654 57.03169836 -16.05414718 55.14074491 -18.66015625 52.54296875 C-19.83868164 51.36831055 -19.83868164 51.36831055 -21.04101562 50.16992188 C-21.85248047 49.35072266 -22.66394531 48.53152344 -23.5 47.6875 C-24.31533203 46.87990234 -25.13066406 46.07230469 -25.97070312 45.24023438 C-26.75509766 44.45326172 -27.53949219 43.66628906 -28.34765625 42.85546875 C-29.41705444 41.78268677 -29.41705444 41.78268677 -30.50805664 40.68823242 C-36.24409544 34.18284007 -36.86254633 28.32958628 -36.77734375 19.89453125 C-36.00567055 12.18666904 -32.25404279 6.5262427 -26.37890625 1.65625 C-18.97059305 -2.75594458 -7.92679982 -3.50578622 0 0 Z",
        {
          fillColor: fillColor,
          fillOpacity: 1,
          scale: 0.8,
          rotation: 0,
          strokeWeight: 0,
          strokeOpacity: 0.1,
          anchor: new BMap.Size(-10, 60),
        }
      );
      return icons;
    }
    return null;
  };

  const getLabel = (number = 0) => {
    // 确保BMap对象存在后再使用
    if (!window.BMap) return null;

    var offsetSize = new BMap.Size(10, 10);
    var labelStyle = {
      color: "#fff",
      backgroundColor: "0.05",
      border: "0",
      fontSize: "24px",
    };
    switch ((number + "").length) {
      case 1:
        labelStyle.fontSize = "24px";
        offsetSize = new BMap.Size(12, 10);
        break;
      case 2:
        labelStyle.fontSize = "18px";
        offsetSize = new BMap.Size(10, 12);
        break;
      case 3:
        labelStyle.fontSize = "16px";
        offsetSize = new BMap.Size(7, 11);
        break;
      case 4:
        labelStyle.fontSize = "14px";
        offsetSize = new BMap.Size(5, 11);
        break;
      default:
        break;
    }
    var label = new BMap.Label(number, {
      offset: offsetSize,
    });
    label.setStyle(labelStyle);
    return label;
  };

  const addMarker = (position, element) => {
    // 确保BMap对象和map对象存在后再使用
    if (!window.BMap || !map) return null;

    const markerPoint = new BMap.Point(position.lng, position.lat);
    let icons = getSvgIcon(element.yearsType);
    const marker = new BMap.Marker(markerPoint, {
      icon: icons,
      title: element.address + " " + element.storeName,
    });

    marker.addEventListener("click", () => {
      map.setZoom(18);
      map.setCenter(marker.getPosition());
    });

    marker.setLabel(getLabel(element.total));
    if (map) {
      map.addOverlay(marker);
      return marker;
    } else {
      return null;
    }
  };

  const renderMakrk = async () => {
    if (showData.length > 0) {
      markers.map((item) => {
        if (item && typeof item.remove === "function") {
          item.remove();
        }
      });
      setMarkers([]);
      let markerList = showData
        .map((element, index) => {
          let local = element.location.split(",");
          let location = { lng: Number(local[0]), lat: Number(local[1]) };
          return addMarker(location, element);
        })
        .filter(Boolean); // 过滤掉null值
      setMarkers(markerList);
    } else {
      markers.map((item) => {
        if (item && typeof item.remove === "function") {
          item.remove();
        }
      });
      setMarkers([]);
    }
  };

  useEffect(() => {
    renderMakrk();
  }, [showData]);

  const filterData = (list) => {
    let temp = {};
    list.forEach((item) => {
      let id = item.id;
      let obj = temp[id];
      if (!obj) {
        temp[id] = item;
      } else {
        let current = Number(item.yearsType);
        let tempValue = Number(obj.yearsType);
        let total = item.total + obj.total;
        if (current > tempValue) {
          item.total = total;
          temp[id] = item;
        }
      }
    });
    let result = Object.keys(temp).map((key) => {
      return temp[key];
    });
    return result;
  };

  useEffect(() => {
    let dataList = [
      ...(props.mapData?.zeroToThreeYears || []),
      ...(props.mapData?.threeToSixYears || []),
      ...(props.mapData?.sixPlusYears || []),
    ];
    let result = filterData(dataList);
    setDataList(result);
    let list = dataList?.filter((item) => {
      let index = showType.indexOf(item.yearsType);
      if (index > -1) {
        return true;
      } else {
        return false;
      }
    });
    setShowData(list);
  }, [props.mapData]);

  useEffect(() => {
    let list = dataList?.filter((item) => {
      let index = showType.indexOf(item.yearsType);
      if (index > -1) {
        return true;
      } else {
        return false;
      }
    });
    setShowData(list);
    // 只有在showType改变时才更新sessionStorage
    sessionStorage.setItem("showType", showType.toString());
  }, [showType, dataList]); // 添加dataList依赖项

  const fitBounds = (bPoints) => {
    if (map && window.BMap) {
      /* eslint-disable no-eval */
      const { zoom, center } = map.getViewport(eval(bPoints));
      setZoom(zoom);
      map.centerAndZoom(center, zoom);
    }
  };

  useEffect(() => {
    if (showData.length > 1) {
      let resultPoints = showData
        .map((item) => {
          let location = item.location.split(",");
          if (location.length === 2) {
            let p = { lng: Number(location[0]), lat: Number(location[1]) };
            // 确保BMap对象存在后再使用
            if (window.BMap) {
              let markerPoint = new BMap.Point(p.lng, p.lat);
              return markerPoint;
            }
          } else {
            return null;
          }
          return null;
        })
        .filter(Boolean); // 过滤掉null值

      if (map && resultPoints.length > 0) {
        fitBounds(resultPoints);
      }
    } else if (showData.length === 1) {
      try {
        let location = showData[0].location.split(",");
        if (location.length === 2) {
          let p = { lng: Number(location[0]), lat: Number(location[1]) };
          // 确保BMap对象存在后再使用
          if (window.BMap) {
            const point = new BMap.Point(p.lng, p.lat);
            map?.panTo(point);
          }
        }
      } catch (e) {
        console.log(e);
      }
    }
  }, [showData, map]); // 添加map依赖项

  useEffect(() => {
    if (center && window.BMap) {
      let location = center.split(",");
      if (location.length === 2) {
        position = { lng: Number(location[0]), lat: Number(location[1]) };
        const point = new BMap.Point(position.lng, position.lat);
        map && map.setZoom(1);
        map && map.panTo(point);
      }
    }
  }, [center, map]); // 添加map依赖项

  const initMap = () => {
    // 确保BMap对象存在后再使用
    if (!window.BMap) {
      console.error("Baidu Map API is not loaded yet");
      return;
    }

    const map = new BMap.Map("mapContainer");
    map.centerAndZoom(
      new BMap.Point(118.06088011225243, 24.618211693728895),
      zoom
    );
    map.enableScrollWheelZoom(true);
    var zoomCtrl = new BMap.NavigationControl(); // 添加缩放控件
    map.addControl(zoomCtrl);

    var fullscreenControl = new FullscreenControl();
    //添加到地图中
    map.addControl(fullscreenControl);

    var yearControl = new YearControl();
    //添加到地图中
    map.addControl(yearControl);

    setMap(map);
  };

  useEffect(() => {
    // 先加载百度地图API
    loadBaiduMapAPI()
      .then(() => {
        setBmapApiLoaded(true);
      })
      .catch((error) => {
        console.error("Failed to load Baidu Map API:", error);
      });
  }, []);

  useEffect(() => {
    if (bmapApiLoaded && map === null) {
      initMap();
    }
  }, [bmapApiLoaded, map]); // 添加map依赖项

  const showTypeClick = (type) => {
    let showTypeStr = sessionStorage.getItem("showType");
    let tempArray = [];
    if (showTypeStr) {
      tempArray = showTypeStr.split(",");
    }
    let index = tempArray.indexOf(type);
    if (index > -1) {
      tempArray.splice(index, 1);
      // 使用函数式更新确保基于最新状态
      setShowType((prev) => {
        const newArray = [...prev];
        const i = newArray.indexOf(type);
        if (i > -1) {
          newArray.splice(i, 1);
        }
        return newArray;
      });
    } else {
      // 使用函数式更新确保基于最新状态
      setShowType((prev) => [...prev, type]);
    }
  };

  // 添加自定义全屏控件  定义 一个控件类
  function FullscreenControl() {
    // 确保BMAP_ANCHOR_TOP_RIGHT和BMap.Size存在后再使用
    if (window.BMap && typeof BMAP_ANCHOR_TOP_RIGHT !== "undefined") {
      this.defaultAnchor = BMAP_ANCHOR_TOP_RIGHT;
      this.defaultOffset = new BMap.Size(10, 10);
    }
  }
  //通过JavaScript的prototype属性继承于BMap.Control
  if (window.BMap) {
    FullscreenControl.prototype = new BMap.Control();
  }

  //自定义控件必须实现自己的initialize方法，并且将控件的DOM元素返回
  FullscreenControl.prototype.initialize = function (map) {
    //创建一个dom元素
    var div = document.createElement("div");
    div.innerHTML =
      '<svg t="1705989135739" style="width:30px;height:30px;cursor: pointer;"  viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4170" width="200" height="200"><path d="M285.866667 810.666667H384v42.666666H213.333333v-170.666666h42.666667v98.133333l128-128 29.866667 29.866667-128 128z m494.933333 0l-128-128 29.866667-29.866667 128 128V682.666667h42.666666v170.666666h-170.666666v-42.666666h98.133333zM285.866667 256l128 128-29.866667 29.866667-128-128V384H213.333333V213.333333h170.666667v42.666667H285.866667z m494.933333 0H682.666667V213.333333h170.666666v170.666667h-42.666666V285.866667l-128 128-29.866667-29.866667 128-128z" fill="#444444" p-id="4171"></path></svg>';
    // 设置样式
    div.style.cursor = "pointer";
    div.style.padding = "8px 13px";
    div.style.margin = "20px 0px";
    div.style.boxShadow = "0 2px 6px 0 rgba(27, 142, 236, 0.5)";
    div.style.borderRadius = "5px";
    div.style.backgroundColor = "white";
    return div;
  };

  function YearControl() {
    // 确保BMap和BMAP_ANCHOR_BOTTOM_RIGHT存在后再使用
    if (window.BMap && typeof BMAP_ANCHOR_BOTTOM_RIGHT !== "undefined") {
      this.defaultAnchor = BMAP_ANCHOR_BOTTOM_RIGHT;
      this.defaultOffset = new BMap.Size(120, 10);
    }
  }

  //通过JavaScript的prototype属性继承于BMap.Control
  if (window.BMap) {
    YearControl.prototype = new BMap.Control();
  }

  //自定义控件必须实现自己的initialize方法，并且将控件的DOM元素返回
  YearControl.prototype.initialize = function (map) {
    const controlBox = document.createElement("div");
    controlBox.style.boxShadow =
      "0px 2px 1px -1px rgba(0,0,0,0.2), 0px 1px 1px 0px rgba(0,0,0,0.14), 0px 1px 3px 0px rgba(0,0,0,0.12)";
    controlBox.style.padding = "10px 15px";
    controlBox.style.borderRadius = "5px";
    controlBox.style.background = "#ffffff";
    controlBox.style.marginBottom = "20px";
    const legendBox = document.createElement("div");
    legendBox.style.display = "flex";

    // Create the control.
    const control1 = createCenterControl("1");
    // Append the control to the DIV.
    legendBox.appendChild(control1);

    const control2 = createCenterControl("2");
    legendBox.appendChild(control2);

    const control3 = createCenterControl("3");
    legendBox.appendChild(control3);

    const title1 = document.createElement("div");
    title1.textContent = t("mapDashboard.additional_information"); //  'Additional Information'
    title1.style.fontWeight = 600;
    title1.style.fontSize = "18px";

    const title2 = document.createElement("div");
    title2.textContent = t("mapDashboard.digital_signage_usage_duration");
    title2.style.fontWeight = 400;
    title2.style.fontSize = "14px";
    title2.style.marginTop = "10px";

    const title3 = document.createElement("div");
    title3.textContent = t("mapDashboard.markers_number_tip");
    title3.style.fontWeight = 400;
    title3.style.fontSize = "12px";
    title3.style.marginTop = "10px";
    title3.style.marginBottom = "10px";

    controlBox.appendChild(title1);
    controlBox.appendChild(title2);
    controlBox.appendChild(title3);
    controlBox.appendChild(legendBox);

    // 将控件添加到地图上
    map.getContainer().appendChild(controlBox);
    return controlBox;
  };

  const createCenterControl = (yearsType) => {
    // 确保BMap对象存在后再使用
    if (!window.BMap) return null;

    const controlButton = document.createElement("button");
    controlButton.style.backgroundColor = "#ffffff";
    controlButton.style.border = "none";
    controlButton.style.outline = "none";
    controlButton.style.padding = "5px";
    controlButton.style.marginLeft = "10px";
    controlButton.style.borderRadius = "5px";
    controlButton.style.boxShadow =
      "0px 2px 1px -1px rgba(0,0,0,0.2), 0px 1px 1px 0px rgba(0,0,0,0.14), 0px 1px 3px 0px rgba(0,0,0,0.12)";
    controlButton.style.cursor = "pointer";
    controlButton.style.display = "flex";
    controlButton.style.alignItems = "center";

    let icon = "";
    if (yearsType === "1") {
      icon = pin_green;
    } else if (yearsType === "2") {
      icon = pin_blue;
    } else if (yearsType === "3") {
      icon = pin_orange;
    }
    const img = document.createElement("img");
    img.src = icon;
    img.style.width = "20px";
    img.style.height = "20px";

    let label = "";
    if (yearsType === "1") {
      label = "0-3Yr";
    } else if (yearsType === "2") {
      label = "3-6Yr";
    } else if (yearsType === "3") {
      label = "6+Yr";
    }
    const pNode = document.createElement("p");
    pNode.textContent = label;
    pNode.style.fontSize = "15px";
    pNode.style.marginLeft = "5px";

    controlButton.appendChild(img);
    controlButton.appendChild(pNode);
    controlButton.addEventListener("click", (e) => {
      if (img.style.opacity === "0.5") {
        img.style.opacity = 1;
      } else {
        img.style.opacity = 0.5;
      }
      showTypeClick(yearsType);
    });
    return controlButton;
  };

  return (
    <div
      id="mapContainer"
      style={{ height: "calc(100vh - 180px)", width: "100%" }}
    />
  );
};

export default BaiDuMap;
