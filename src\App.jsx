import ThemeCustomization from "@/themes";
import React, { useEffect } from "react";
import ScrollTop from "@/components/ScrollTop";

import { ConfirmProvider } from "@/components/zkconfirm";
// import { getStoreLang } from "@/src/utils/langUtils";
// 路由鉴权
import RouterWaiter from "@/components/routerWaiter";
import routes from "@/router/routers";
import onRouteBefore from "@/router/onRouteBefore";
import Loader from "@/components/Loader";

import { ToastContainer, Zoom, Slide } from "react-toastify";
import { useDispatch } from "react-redux";
import { setMenuList } from "@/store/reducers/menu";
import { getUserMenus } from "@/service/api/L3Sevice";
import { useNavigate } from "react-router-dom";
import { withTranslation } from "react-i18next";
import { preloadBaiduMapAPI } from "@/utils/baiduMapLoader"; // 引入百度地图预加载器
import i18n from "i18next";
import {
  initNavigationHandler,
  clearNavigationMarkers,
} from "@/utils/navigationHandler";

function App() {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  useEffect(() => {
    // 创建一个标志，标识组件是否已挂载
    let isMounted = true;

    // 预加载百度地图API
    preloadBaiduMapAPI();

    const fetchData = async () => {
      try {
        const menuRes = await getUserMenus({ applicationCode: "SD" });
        // 只有在组件仍然挂载时才更新状态
        if (isMounted) {
          let menus = menuRes.data;
          dispatch(setMenuList(menus));
          if (menuRes.code == "AUTH000019") {
            navigate("/login");
          }
        }
      } catch (error) {
        // 只有在组件仍然挂载时才记录错误
        if (isMounted) {
          console.error("Error fetching user menus:", error);
        }
      }
    };

    fetchData();

    // 清理函数，组件卸载时设置isMounted为false
    return () => {
      isMounted = false;
    };
  }, []); // 确保依赖数组为空，只在组件挂载时执行一次

  // 页面加载时的初始化
  useEffect(() => {
    // 清理过期的导航标记（超过10分钟的标记）
    const languageChangeTime = sessionStorage.getItem("languageChangeTime");
    if (languageChangeTime) {
      const now = Date.now();
      const timeDiff = now - parseInt(languageChangeTime);
      // 如果超过10分钟，清理标记
      if (timeDiff > 10 * 60 * 1000) {
        clearNavigationMarkers();
        console.log("清理过期的导航标记");
      }
    }
  }, []);

  // 添加对浏览器回退事件和导航的监听
  useEffect(() => {
    let currentLanguage = i18n.language;

    const handlePopState = () => {
      // 检查语言是否发生了变化
      const storedLanguage = localStorage.getItem("zkdigimax_sd_lang");
      if (storedLanguage && storedLanguage !== currentLanguage) {
        // 如果语言发生变化，重新加载页面以确保应用状态一致
        window.location.reload();
        return;
      }
    };

    // 监听浏览器的回退/前进事件（仅处理语言变化）
    window.addEventListener("popstate", handlePopState);

    // 当语言改变时更新currentLanguage变量
    const handleLanguageChange = () => {
      currentLanguage = i18n.language;
    };

    // 监听i18n语言改变事件
    i18n.on("languageChanged", handleLanguageChange);

    // 初始化导航处理器（处理从子应用返回主应用的逻辑）
    const cleanupNavigationHandler = initNavigationHandler();

    // 清理事件监听器
    return () => {
      window.removeEventListener("popstate", handlePopState);
      i18n.off("languageChanged", handleLanguageChange);

      // 清理导航处理器
      if (cleanupNavigationHandler) {
        cleanupNavigationHandler();
      }
    };
  }, []);

  return (
    <ThemeCustomization>
      <ConfirmProvider>
        <ScrollTop>
          <RouterWaiter
            routes={routes}
            loading={<Loader />}
            onRouteBefore={onRouteBefore}
          />

          <ToastContainer
            position="top-center"
            style={{
              fontSize: "16px",
            }}
            autoClose={2000}
            hideProgressBar
            newestOnTop={false}
            closeOnClick
            rtl={false}
            limit={3}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="light"
            transition={Slide}
          />
        </ScrollTop>
      </ConfirmProvider>
    </ThemeCustomization>
  );
}

export default withTranslation()(App);
