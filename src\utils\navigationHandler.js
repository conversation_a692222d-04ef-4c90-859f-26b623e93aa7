/**
 * 导航处理工具
 * 专门处理微前端环境中的导航和页面刷新逻辑
 */

/**
 * 检查是否在qiankun环境中
 */
export const isInQiankunEnv = () => {
  return window.__POWERED_BY_QIANKUN__;
};

/**
 * 检查当前URL是否为主应用中心页面
 */
export const isMainAppCenterPage = () => {
  const currentUrl = window.location.href;
  return currentUrl.includes('/application/center');
};

/**
 * 检查是否从子应用返回主应用
 * 通过检查URL变化和历史记录来判断
 */
export const isReturnFromSubApp = () => {
  // 检查当前是否在主应用中心页面
  if (!isMainAppCenterPage()) {
    return false;
  }
  
  // 检查是否在qiankun环境中
  if (!isInQiankunEnv()) {
    return false;
  }
  
  // 检查历史记录中是否有子应用的痕迹
  const hasSubAppHistory = sessionStorage.getItem('hasSubAppHistory') === 'true';
  
  return hasSubAppHistory;
};

/**
 * 标记进入子应用
 */
export const markEnterSubApp = () => {
  sessionStorage.setItem('hasSubAppHistory', 'true');
  sessionStorage.setItem('lastSubAppTime', Date.now().toString());
};

/**
 * 清除子应用历史标记
 */
export const clearSubAppHistory = () => {
  sessionStorage.removeItem('hasSubAppHistory');
  sessionStorage.removeItem('lastSubAppTime');
};

/**
 * 处理从子应用返回主应用的逻辑
 */
export const handleReturnToMainApp = () => {
  if (isReturnFromSubApp()) {
    console.log('检测到从子应用返回主应用中心页面，准备刷新');
    
    // 清除子应用历史标记
    clearSubAppHistory();
    
    // 延迟刷新，确保URL完全更新
    setTimeout(() => {
      console.log('执行页面刷新');
      window.location.reload();
    }, 150);
    
    return true;
  }
  
  return false;
};

/**
 * 初始化导航处理器
 * 监听popstate事件并处理返回主应用的情况
 */
export const initNavigationHandler = () => {
  // 如果不在qiankun环境中，不需要处理
  if (!isInQiankunEnv()) {
    return null;
  }
  
  const handlePopState = (event) => {
    console.log('检测到popstate事件', {
      currentUrl: window.location.href,
      isMainAppCenter: isMainAppCenterPage(),
      hasSubAppHistory: sessionStorage.getItem('hasSubAppHistory')
    });
    
    // 处理从子应用返回主应用的情况
    handleReturnToMainApp();
  };
  
  // 监听popstate事件
  window.addEventListener('popstate', handlePopState);
  
  // 监听子应用挂载事件
  const handleSubAppMount = () => {
    markEnterSubApp();
    console.log('标记进入子应用');
  };
  
  // 监听自定义的子应用挂载事件
  window.addEventListener('sub-app-mounted', handleSubAppMount);
  
  // 返回清理函数
  return () => {
    window.removeEventListener('popstate', handlePopState);
    window.removeEventListener('sub-app-mounted', handleSubAppMount);
  };
};

/**
 * 触发子应用挂载事件
 * 在子应用挂载时调用
 */
export const triggerSubAppMountEvent = () => {
  window.dispatchEvent(new CustomEvent('sub-app-mounted', {
    detail: { timestamp: Date.now() }
  }));
};
