/**
 * 导航处理工具
 * 专门处理微前端环境中的导航和页面刷新逻辑
 */

/**
 * 检查是否在qiankun环境中
 */
export const isInQiankunEnv = () => {
  return window.__POWERED_BY_QIANKUN__;
};

/**
 * 检查当前URL是否为主应用中心页面
 */
export const isMainAppCenterPage = () => {
  const currentUrl = window.location.href;
  return currentUrl.includes('/application/center');
};

/**
 * 检查是否需要刷新页面
 * 包括从子应用返回主应用和语言切换后的回退情况
 */
export const shouldRefreshPage = () => {
  // 检查当前是否在主应用中心页面
  if (!isMainAppCenterPage()) {
    return false;
  }

  // 检查是否在qiankun环境中
  if (!isInQiankunEnv()) {
    return false;
  }

  // 情况1：从子应用返回主应用
  const hasSubAppHistory = sessionStorage.getItem('hasSubAppHistory') === 'true';

  // 情况2：语言切换后的回退
  const languageChanged = sessionStorage.getItem('languageChanged') === 'true';
  const languageChangeTime = sessionStorage.getItem('languageChangeTime');
  const now = Date.now();

  // 如果语言切换时间在5分钟内，认为可能需要刷新
  const isRecentLanguageChange = languageChangeTime &&
    (now - parseInt(languageChangeTime)) < 5 * 60 * 1000;

  return hasSubAppHistory || (languageChanged && isRecentLanguageChange);
};

/**
 * 检查是否从子应用返回主应用（保持向后兼容）
 */
export const isReturnFromSubApp = () => {
  return shouldRefreshPage();
};

/**
 * 标记进入子应用
 */
export const markEnterSubApp = () => {
  sessionStorage.setItem('hasSubAppHistory', 'true');
  sessionStorage.setItem('lastSubAppTime', Date.now().toString());
};

/**
 * 清除所有导航相关的标记
 */
export const clearNavigationMarkers = () => {
  sessionStorage.removeItem('hasSubAppHistory');
  sessionStorage.removeItem('lastSubAppTime');
  sessionStorage.removeItem('languageChanged');
  sessionStorage.removeItem('languageChangeTime');
};

/**
 * 清除子应用历史标记（保持向后兼容）
 */
export const clearSubAppHistory = () => {
  clearNavigationMarkers();
};

/**
 * 处理需要刷新页面的情况
 */
export const handlePageRefresh = () => {
  if (shouldRefreshPage()) {
    const hasSubAppHistory = sessionStorage.getItem('hasSubAppHistory') === 'true';
    const languageChanged = sessionStorage.getItem('languageChanged') === 'true';

    let reason = '';
    if (hasSubAppHistory && languageChanged) {
      reason = '从子应用返回且语言已切换';
    } else if (hasSubAppHistory) {
      reason = '从子应用返回主应用中心页面';
    } else if (languageChanged) {
      reason = '语言切换后回退到主应用中心页面';
    }

    console.log(`检测到需要刷新页面：${reason}`);

    // 清除所有导航标记
    clearNavigationMarkers();

    // 延迟刷新，确保URL完全更新
    setTimeout(() => {
      console.log('执行页面刷新');
      window.location.reload();
    }, 150);

    return true;
  }

  return false;
};

/**
 * 处理从子应用返回主应用的逻辑（保持向后兼容）
 */
export const handleReturnToMainApp = () => {
  return handlePageRefresh();
};

/**
 * 初始化导航处理器
 * 监听popstate事件并处理返回主应用的情况
 */
export const initNavigationHandler = () => {
  // 如果不在qiankun环境中，不需要处理
  if (!isInQiankunEnv()) {
    return null;
  }

  const handlePopState = (event) => {
    console.log('检测到popstate事件', {
      currentUrl: window.location.href,
      isMainAppCenter: isMainAppCenterPage(),
      hasSubAppHistory: sessionStorage.getItem('hasSubAppHistory'),
      languageChanged: sessionStorage.getItem('languageChanged'),
      languageChangeTime: sessionStorage.getItem('languageChangeTime')
    });

    // 处理需要刷新页面的情况
    handlePageRefresh();
  };

  // 监听popstate事件
  window.addEventListener('popstate', handlePopState);

  // 监听子应用挂载事件
  const handleSubAppMount = () => {
    markEnterSubApp();
    console.log('标记进入子应用');
  };

  // 监听自定义的子应用挂载事件
  window.addEventListener('sub-app-mounted', handleSubAppMount);

  // 返回清理函数
  return () => {
    window.removeEventListener('popstate', handlePopState);
    window.removeEventListener('sub-app-mounted', handleSubAppMount);
  };
};

/**
 * 触发子应用挂载事件
 * 在子应用挂载时调用
 */
export const triggerSubAppMountEvent = () => {
  window.dispatchEvent(new CustomEvent('sub-app-mounted', {
    detail: { timestamp: Date.now() }
  }));
};
