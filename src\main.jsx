import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import { setToken } from "@/utils/auth";
import i18n from "./lang/index";
import { I18nextProvider } from "react-i18next";
import "@/lang/index";
import { Provider as ReduxProvider } from "react-redux";
import {
  renderWithQiankun,
  qiankunWindow,
} from "vite-plugin-qiankun/dist/helper";
import { store } from "@/store";
import { useDispatchUser } from "@/hooks/user";
import { BrowserRouter as Router } from "react-router-dom";
import "@/assets/css/global.less";
import "virtual:svg-icons-register";
import "@/utils/hmr"; // 引入HMR辅助工具
import { preloadBaiduMapAPI } from "@/utils/baiduMapLoader"; // 引入百度地图预加载器
import { initQiankunCompat } from "@/utils/qiankunCompat"; // 引入qiankun兼容性修复
import { initSubAppI18n, cleanupI18nSync } from "@/utils/microAppI18nSync";

const initQianKun = () => {
  renderWithQiankun({
    // 当前应用在主应用中的生命周期
    // 文档 https://qiankun.umijs.org/zh/guide/getting-started#
    mount(props) {
      try {
        render(props.container);
        // 初始化国际化同步
        const i18nConfig = initSubAppI18n(i18n, props);
        console.log("国际化同步配置:", i18nConfig);
        props.onGlobalStateChange((state) => {
          if (state?.token) {
            setToken(state.token);
            sessionStorage.setItem(
              "USER_INFO",
              JSON.stringify(state?.userInfo)
            );
          }
        }, true);
      } catch (error) {
        console.error("子应用挂载时发生错误:", error);
      }
    },
    bootstrap() {},
    unmount(props) {
      console.log("子应用正在卸载");
      // 清理root实例，为下次挂载做准备
      if (root) {
        root.unmount();
        root = null;
      }
      cleanupI18nSync(props);

      // 清理可能存在的定时器
      if (typeof window !== "undefined") {
        for (let i = 1; i < 1000; i++) {
          clearInterval(i);
          clearTimeout(i);
        }
      }
    },
  });
};

// 在其他地方监听挂载完成事件
window.addEventListener("cms-app", () => {
  console.log("子应用挂载完成");
  // 执行挂载完成后的操作
});

let root = null;
// 添加一个标志位，标识应用是否已挂载
let isAppMounted = false;

const render = (container, props = null) => {
  // 如果是在主应用的环境下就挂载主应用的节点，否则挂载到本地
  const appDom = container ? container : document.getElementById("root");

  // 避免重复创建root，支持HMR
  if (!root) {
    root = ReactDOM.createRoot(appDom);
  }

  // 只有在应用未挂载时才进行渲染
  if (!isAppMounted) {
    isAppMounted = true;
    root.render(
      <ReduxProvider store={store}>
        <Router basename="/cms-app">
          <I18nextProvider i18n={i18n}>
            <App props={props} />
          </I18nextProvider>
        </Router>
      </ReduxProvider>
    );
  }
};

// 初始化qiankun兼容性修复
initQiankunCompat();

// 预加载百度地图API
preloadBaiduMapAPI();

if (!qiankunWindow.__POWERED_BY_QIANKUN__) {
  // 独立运行模式，HMR 直接生效
  render();
} else {
  initQianKun();
}
