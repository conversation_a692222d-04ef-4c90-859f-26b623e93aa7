import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import qiankun from "vite-plugin-qiankun";
import { resolve } from "path";
import { name } from "./package.json";
import AutoImport from "unplugin-auto-import/vite";
import tailwindcss from "tailwindcss";
import autoprefixer from "autoprefixer";
import cssInjectedByJsPlugin from "vite-plugin-css-injected-by-js";
import { wrapperEnv } from "./src/utils/getEnv";
import svgr from "vite-plugin-svgr";
import { setupUnPluginIcon } from "./build/icon.js";

let localEnv;
try {
  localEnv = (await import("./local-env.js")).default;
} catch (error) {
  /* empty */
}
export default defineConfig((mode) => {
  const env = loadEnv(mode.mode, process.cwd());
  const viteEnv = wrapperEnv(env);

  // 检查是否在qiankun环境中
  const isQiankunMode = process.env.QIANKUN === '1';



  return {
    // base: viteEnv.BUILD_PATH,
    base: process.env.NODE_ENV === "development" ? "./" : "/cms-app",
    resolve: {
      alias: {
        "@": resolve(__dirname, "./src"),
      },
    },

    plugins: [
      // 在qiankun模式下禁用React Refresh但保留JSX转换
      // https://github.com/umijs/qiankun/issues/1257
      react({
        fastRefresh: !isQiankunMode, // 在qiankun模式下禁用fastRefresh
        jsxRuntime: 'automatic', // 使用自动JSX运行时
        jsxImportSource: 'react', // 确保JSX导入源
      }),
      AutoImport({
        // global imports to register
        imports: ["react", "react-router-dom", "react-i18next"],
      }),
      setupUnPluginIcon(viteEnv),
      qiankun("cms-app", {
        // 微应用名字，与主应用注册的微应用名字保持一致
        useDevMode: true,
        // 修复ES模块兼容性问题
        devSandbox: false,
      }),

      svgr({
        svgrOptions: {},
      }),
      cssInjectedByJsPlugin(),
    ].filter(Boolean), // 过滤掉false值
    css: {
      postcss: {
        plugins: [tailwindcss, autoprefixer],
      },
    },
    server: {
      port: 8081,
      host: "0.0.0.0",
      cors: true,
      headers: {
        "Access-Control-Allow-Origin": "*",
      },
      origin: "http://localhost:8081",
      hmr: {
        overlay: false, // 关闭 HMR 报错时的全屏遮罩
        clientPort: 8081, // 确保和子应用的端口一致
        host: "localhost", // 明确指定HMR主机
        port: 8081, // HMR端口
      }, // 启用 HMR
      proxy: {
        "/dev": {
          // 推荐不要直接修改下面的地址，查看同级目录下的local-env.js.sample文件介绍
          target: "http://**********:9090",
          // target: "http://**********:9090",
          // target: "http://***********:9090",
          // target: "http://**********:9090",
          // target: "http://**********:9092",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/dev/, ""),
        },
      },
    },
    build: process.env.QIANKUN
      ? {
        target: "esnext",
        cssCodeSplit: false,
        rollupOptions: {
          output: {
            format: "umd",
            name: name,
            entryFileNames: `${name}-[name].js`,
            chunkFileNames: `${name}-[name].js`,
            assetFileNames: `${name}-[name].[ext]`,
            globals: {
              react: "React",
              "react-dom": "ReactDOM",
              "react-dom/client": "ReactDOM",
            },
          },
          external:  [],
        },
      }
      : undefined,

    // 修复qiankun环境下的模块兼容性
    define: {
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
      global: 'globalThis',
      // 确保React在全局可用
      ...(isQiankunMode ? {
        __DEV__: process.env.NODE_ENV === 'development',
      } : {}),
    },

    // 确保React相关模块正确解析
    optimizeDeps: {
      include: ['react', 'react-dom',"react-dom/client"],
      force: isQiankunMode, // 在qiankun模式下强制重新优化依赖
    },
    
    // 添加此配置以解决生产环境下的React错误
    esbuild: {
      drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : []
    }
  }
});