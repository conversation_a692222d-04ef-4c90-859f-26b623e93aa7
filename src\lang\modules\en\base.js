import { private_agree_text, user_agree_text } from './data'

const en = {
  //公用国际化
  common: {
    common_area_name_regTip:
      "Region names can only start with letters, Chinese characters, and can only contain letters, numbers, Chinese spaces, and underscores",
    name_max: "The length of the name must not exceed 50 words",
    common_login_tips_have_account: "Already have an account?",
    common_login_tips_dont_have_account: `Don't have an account?`,
    common_tabs_account_login: "Account <PERSON><PERSON>",
    common_tabs_quick_login: "Quick Login",
    common_noResults_found: "No matching data",
    common_piece: "Piece",
    common_login: "Login",
    common_register: "Sign up",
    common_login_speech: "Make Information Publishing Easier",
    common_ZKBioMedia: "ZKBio Media",
    common_lonLat: "Latitude and Longitude",
    common_latitude: "Latitude",
    common_longitude: "Longitude",
    common_osVersion: "System Version",
    common_macAddress: "MAC Address",
    common_switcher_timing: "Scheduled Working Duration",
    common_switcher_shutdown: "Scheduled Shutdown",
    common_switcher_startTiming: "Starting Time",
    common_switcher_shutTiming: "Shutdown Time",
    common_select_startTiming: "Starting Time",
    common_select_shutTiming: "Please select the shutdown time",
    common_downloading: "Downloading",
    common_downloaded: "Downloaded",
    common_program: "Program",
    common_template: "Template",
    common_scheduling: "Scheduling",
    common_custom: "Customization",
    common_top: "Top",
    common_bottom: "Bottom",
    common_speed: "Speed",
    common_slow: "Slow",
    common_general: "Moderate",
    common_fast: "Fast",
    common_faster: "Rapid",
    common_direction: "Direction",
    common_up: "Up",
    common_down: "Down",
    common_left: "Left",
    common_right: "Right",
    common_left_to_right: "From left to right",
    common_right_to_left: "From right to left",
    common_expired: "Expired",
    common_more: "More",
    common_enable_group: "Enable Grouping",
    common_stop_group: "Disable Grouping",
    common_clear_program: "Clear Program",
    common_auth: "Authenticate",
    common_play_control: "Play Control",
    common_screen_control: "Screen Control",
    common_volume_control: "Volume Control",
    common_volume_mute: "Mute",
    common_volume_set: "Volume Settings",
    common_power_control: "Power Control",
    common_device_reboot: "Reboot Device",
    common_device_timing: "Device Timing",
    common_device_playStart: "Start Playing",
    common_device_playStop: "Stop Playing",
    common_device_appUpgrade: "Upgrade Application",
    common_device_modeSet: "Mode",
    common_device_backlightSet: "Backlight",
    common_device_brightnessSet: "Brightness",
    common_device_rotateSet: "Rotation",
    common_device_HAVSet: "Horizontal or Vertical",
    common_device_rotation: "Screen Rotation",
    common_device_rotationDegree: "Rotational Angle",
    common_device_modeSD: "SD Card ",
    common_device_modeCloud: "Server Mode",
    common_prompt_title: "Reminder",
    common_prompt_warning: "Warning",
    common_prompt_error: "Error",
    common_prompt_noData: "No Data",
    common_param_width: "Width",
    common_param_height: "Height",
    common_upload_success: "Successfully Uploaded!",
    common_upload_failed: "Failed to Upload!",
    common_upload_image_message: "Please upload an image less than 10MB",
    common_upload_material_message: "Please upload material less than 1GB",
    common_upload_message: "Upload Files",
    common_upload_dept_message: "Please choose, the agency is being imported",
    common_add_success: "Successfully Added!",
    common_add_failed: "Failed to Add",
    common_edit_success: "Successfully Edited!",
    common_edit_failed: "Failed to Edit!",
    common_del_success: "Deleted",
    common_del_failed: "Failed to Delete!",
    common_save_success: "Saved",
    common_save_failed: "Failed to Save!",
    common_auth_success: "Verified",
    common_edit_group: "Grouping",
    common_edit_save: "Save",
    common_edit_ok: "OK",
    common_edit_cancel: "Cancel",
    common_verification: "Accepted",
    common_deny: "Denied",
    common_none: "None",
    common_op_success: "Successful",
    common_op_fail: "Failed",
    common_op_refresh: "Refresh",
    common_op_new: "Add",
    common_op_del: "Delete",
    common_op_publish: "Publish",
    common_op_modify: "Modify",
    common_op_edit: "Edit",
    common_op_preview: "Preview",
    common_op_detail: "Details",
    common_op_view: "View",
    common_op_move: "Move",
    common_op_search: "Search",
    common_op_reset: "Reset",
    common_op_return: "Return",
    common_op_exit: "Exit",
    common_op_hiddenSearch: "Hide Search",
    common_op_showSearch: "Search",
    common_createdDate: "Create Date",
    common_createdTime: "Create Time",
    common_uploadTime: "Update Time",
    common_startDate: "Starting Date",
    common_endDate: "Ending Date",
    common_startTime: "Start Time",
    common_endTime: "End Time",
    common_date_interval: "Date Interval",
    common_time_interval: "Time Interval",
    common_select_dateInterval: "Select the Range of Dates",
    common_select_timeScope: "Select Time Scope",
    common_select_startTime: "Starting Time",
    common_select_endTime: "Ending Time",
    common_now: "Now",
    common_monday: "Monday",
    common_tuesday: "Tuesday",
    common_wednesday: "Wednesday",
    common_thursday: "Thursday",
    common_friday: "Friday",
    common_saturday: "Saturday",
    common_sunday: "Sunday",
    common_close: "Close",
    common_open: "Open",
    common_download: "Download",
    common_finish: "Finish",
    common_op_export: "Export",
    common_op_import: "Import",
    common_audit: "Audit",
    common_remark: "Remark",
    common_all: "All",
    common_from: "From",
    common_to: "To",
    common_select: "Select",
    common_gender: "Gender",
    common_male: "Male",
    common_female: "Female",
    common_relatedOp: "Operation",
    common_opUser: "Operational User",
    common_module: "Public",
    common_name: "Name",
    common_number: "No.",
    common_title: "Title",
    common_default: "Default",
    common_carousel: "Carousel",
    common_caption: "Caption",
    common_car: "Vehicle",
    common_ad: "Advertisement",
    common_leisure: "Idle",
    common_busy: "Busy",
    common_duration: "Duration",
    common_enable: "Enable",
    common_stop: "Stop",
    common_disable: "Disable",
    common_normal: "Normal",
    common_exception: "Exception",
    common_status: "Status",
    common_online: "Online",
    common_offline: "Offline",
    common_unknown: "Unknown",
    common_group: "Group Name",
    common_group_all: "All Groups",
    common_picture: "Picture",
    common_audio: "Audio",
    common_video: "Video",
    common_file: "File",
    common_other: "Others",
    common_size: "Size",
    common_format: "Format",
    common_property: "Property",
    common_value: "Value",
    common_resolution_ratio: "Resolution",
    common_select_place_place: "Please select a place to place",
    common_advertiser: "Advertiser",
    common_select_advertiser: "Select a Advertiser",
    common_minute: "Minute",
    common_second: "Second",
    common_page: "Page",
    common_page_previous: "Previous Page",
    common_page_next: "Next Page",
    common_nextStep: "Next Step",
    common_preStep: "Previous Step",
    common_sort: "Sort",
    common_role: "Role",
    common_remarks: "Remarks",
    common_user_name: "User Name",
    common_user_password: "Password",

    common_user_sex: "Gender",
    common_user_email: "Email",

    common_user_role: "User Role",
    common_mobile: "Mobile Number",
    common_user_status: "User Status",
    common_user_number: "User Code",
    common_user_nickname: "Nickname",
    common_dept_name: "Department",
    common_plan_name: "Schedule name",
    common_ascription_dept: "Department",
    common_input_volume: "Please input the Volume",
    common_input_osVersion: "Please input the System Version",
    common_input_scroll_text: "Please enter the text you want to scroll",
    common_input_macAddress: "Please input the MAC Address",
    common_input_user_password: "Please input the User Password",
    common_width: "Width",
    common_height: "Height",
    common_input_width: "Please input the Width",
    common_input_height: "Please input the Height",
    common_input_resolution_ratio: "Please input the Resolution",
    common_input_group_name: "Please input the Group Name",
    common_input_group_type: "Please input the Group Type",
    common_input_material_name: "Please input the Material",
    common_input_programs_name: "Please input the Program",
    common_input_template_name: "Please input the Template Name",
    common_input_screen_name: "Please input the Screen Name",
    common_input_scheduling_name: "Please input a schedule name",
    common_input_scheduling_type: "Please input the play Plan Type",
    common_input_deviceId: "Please input Device ID",
    common_input_device_name: "Please input the Device Name",
    common_input_device_code: "Please input the Device Code",
    common_input_device_address: "Please input the Device Address",
    common_input_commercial_owner: "Please input the  Principal",
    common_input_version_name: "Please input the Version Name",
    common_input_version_code: "Please input the Version Code",
    common_input_version_appDesc: "Please input the Version Description",
    common_input_log_title: "Please input the Log Title",
    common_input_dept_name: "Please input the Departmental Name",
    common_input_role_name: "Please input the Role Name",
    common_input_user_name: "Please input the User Name",
    common_input_menu_name: "Please input the Menu name",
    common_input_dict_name: "Please input the Dictionary",
    common_input_config_name: "Please input the Configuration Name",
    common_input_config_key: "Please input the Configuration Key",
    common_input_backup_name: "Please input the Backup Name",
    common_input_job_name: "Please input the Task Name",
    common_input_job_groupName: "Please input the task group name",
    common_input_login_user: "Please input the login user",
    common_input_login_address: "Please input the Login Address",
    common_input_name: "Please input the Name",
    common_input_title: "Please input the Title",
    common_input_duration: "Please input the Duration",
    common_input_operName: "Please input the Operator's Name",
    common_input_phone: "Please input the Mobile Number",
    common_input_city: "Please input the City",
    common_input_company_name: "Please input the Company Name",
    common_input_persons: "Please input the number of Persons",
    common_input_contacts: "Please input the Contact Person",
    common_input_email: "Please input the Email",
    common_input_address: "Please input the Address",
    common_input_products_suppliers: "Please input the Supplier's name",
    common_input_user_nickname: "Please input the Nickname",
    common_input_reason: "Please input the reason of the denial",
    common_input_caption_title: "Please input the caption title",
    common_input_caption_connect: "Please input the caption content",
    common_input_playback_duration: "Please input the duration",
    common_input_remarks: "Please input the remark",
    common_input_old_password: "Please input the Old Password",
    common_input_new_password: "Please input the New Password",
    common_input_confirm_password: "Please confirm the password",
    common_select_status: "Please select the status",
    common_select_type: "Please select the type",
    common_select_group: "Please select the group",
    common_select_leftGroup: "Please select a Group on the left",
    common_select_groupOrDevice: "Please select by Group or by device",
    common_select_material: "Please select the material",
    common_select_program_type: "Please select the program type",
    common_select_publish_type: "Please select the publish type",
    common_select_download_status: "Please select the download status",
    common_select_download_time: "Please select the download time",
    common_select_playWeeks: "Please select the playback week",
    common_select_playMode: "Please select the playback mode",
    common_select_captionPosition: "Please select the caption's location",
    common_select_direction: "Please select the direction",
    common_select_speed: "Please select the speed",
    common_select_fontSize: "Please select the font size",
    common_select_fontColor: "Please select the font color",
    common_select_bgColor: "Please select the background color",
    common_select_dept: "Please select the department",
    common_select_device: "Please select the device",
    common_select_device_type: "Please select the device type",
    common_select_rotationDegree: "Please select the rotation degree",
    common_select_device_playMode: "Please select the playback mode",
    common_select_version: "Please select the version",
    common_select_version_type: "Please select the version type",
    common_select_operation_status: "Please select the operation status",
    common_select_dept_status: "Please select the Department status",
    common_select_role_status: "Please select the Role status",
    common_select_user_status: "Please select the User status",
    common_select_menu_status: "Please select the Menu status",
    common_select_dict_status: "Please select the Dictionary status",
    common_select_config_status: "Please select the Config status",
    common_select_config_sys_status:
      "Please define if this is a built-in system",
    common_select_region: "Please select the Region",
    common_select_job_type: "Please select the Task type",
    common_select_job_status: "Please select the Task status",
    common_select_job_executeStatus: "Please select the execute status",
    common_select_login_status: "Please select the login status",
    common_select_continents: "Please select the Continent",
    common_select_country: "Please select the Country",
    common_select_industry: "Please select the Industry",
    common_select_ascription_dept: "Please select the Department",
    common_rule_program_name: "The Program Name cannot be empty",
    common_rule_program_type: "The Program Type cannot be empty",
    common_rule_template_name: "The Template Name cannot be empty",
    common_rule_resolution: "The Resolution cannot be empty",
    common_rule_group_name: "The Group Name cannot be empty",
    common_rule_name_len60: "The Name length cannot exceed 60 digits",
    common_rule_area_len60:
      "The length of the region name cannot exceed 60 digits",
    common_rule_email_len60: "The email length cannot exceed 60 characters",
    common_rule_phone_len60: "The phone length cannot exceed 60 digits",
    common_rule_caption_title: "The Caption title cannot be empty",
    common_rule_caption_connect: "The Caption content cannot be empty",
    common_rule_device_name: "The Device name cannot be empty",
    common_rule_device_code: "The Device code cannot be empty",
    common_rule_device_type: "The Device type cannot be empty",
    common_rule_device_mac: "The MAC address cannot be empty",
    common_rule_device_name_len120:
      "The length of the Device name cannot exceed 30 digits",
    common_rule_device_mac_len255:
      "The length of the MAC address cannot exceed 30 bits",
    common_rule_version_name: "The Version name cannot be empty",
    common_rule_version_code: "The Version code cannot be empty",
    common_rule_version_type: "The Version type cannot be empty",
    common_rule_version_appDesc: "The Version  description cannot be empty",
    common_rule_version_name_len30:
      "The Version name length cannot exceed 30 bits",
    common_rule_version_code_len30:
      "The Version code length cannot exceed 30 bits",
    common_programNotSelect: "Please select at least one program",
    common_deviceNotSelect: "Please select at least one device",
    common_audit_info:
      "Auditors have the responsibility to audit unauthorized, negative, bad information, or pornographic, violent information. We shall be liable for any legal liability and other consequences arising therefrom",
    common_upload_drag: "Drag to Upload File",
    common_upload_click: "Click to Upload",
    common_add_click: "Click to Add",
    common_upload_apk: "Only apk files are allowed to be uploaded",
    common_fontSize: "Font Size",
    common_fontColor: "Font Color",
    common_bgColor: "Background Color",
    common_yes: "Yes",
    common_no: "No",
    common_pre: "Last Step",
    common_next: "Next Step",
    common_submit: "Submit",
    common_no_result_dept: "The agency does not exist",
    common_no_result_group: "The group does not exist",
    common_please_select: "Please choose",
    common_msg_add_success: "Successfully Added!",
    common_msg_update_success: "Successfully Modified!",
    common_msg_del_success: "Successfully Deleted!",
    common_old_password: "Old Password",
    common_new_password: "New Password",
    common_confirm: "Confirm",
    common_confirm_password: "Confirm Password",
    common_update_password: "Update Password",
    common_icon: "Icon",
    common_click_icon: "Select Icon",
    common_path: "Path",
    common_input_path: "Enter the Path",
    common_background_color: "Bg-Color",
    common_input_base_name: "The Name cannot be empty",
    common_input_path_null: "The Path cannot be empty",
    common_path_length: "The Path length cannot exceed 120 bits",
    common_input_sort_null: "Sort cannot be empty",
    common_role_name: "Role Name",
    common_role_status: "Role Status",
    common_role_no: "Role Number",
    common_role_order: "Role Order",
    common_display_order: "Display Order",
    common_data_rights: "Data Authority",
    common_menu_rights: "Menu Authority",
    common_loading: "Loading",
    common_rights_code: "Code Authority",
    common_rights_range: "Scope of Authority",
    common_role_name_null: "Role Name cannot be empty",
    common_success: "Successful",
    common_sort_number: "Sort Number",
    common_failed: "Failed",
    common_upload: "Upload",
    common_share: "Share",
    common_list: "List",
    common_device_total: "Total Devices",
    common_device_online: "Online Devices",
    common_program_total: "Program List",
    common_to_do_list: "To-Do List",
    common_input_code: "Please enter 0 or 1",
    common_input_errorMessage: "Incorrect Volume Format",
    common_device_errorMessage: "No device information. Please try again.",
    common_brightness_errorMessage: "Incorrect Brightness Format",
    common_refresh_page: "Refresh",
    common_close_current: "Close the current page",
    common_close_other: "Close the other page",
    common_close_all: "Close All",
    common_quit_system: "Exit",
    common_confirm_quit_system:
      "Are you sure you to log out and exit the system?",
    common_error_000:
      "The operation is too frequent. Please do not repeat the request",
    common_error_401: "The authorization has expired. Please login again",
    common_error_403: "The current operation has no permission",
    common_error_404: "The resource does not exist",
    common_error_417:
      "The login account is not bound. Please use the password to log in and bind it",
    common_error_423:
      "Demo environment cannot be operated. Please contact ZKTeco staff for details",
    common_error_426:
      "The user name does not exist or the password is incorrect",
    common_error_428: "Verification code error. Please enter again",
    common_error_429: "Request is too frequent",
    common_error_430: "Verification code has expired",
    common_error_479: "Demo environment, no operation permission",
    common_error_default:
      "Unknown system error. Please send feedback to the admin",
    common_login_timeout: "Login Timeout",
    common_product_error:
      "The product authorization is invalid. Please contact the manufacturer",
    common_system_prompt: "System Prompt",
    common_login_again: "Log in again",
    common_company_text: "ZKTECO CO., LTD",
    common_status_text: "Slow Down",
    common_number_text: "Automatic Identification",
    common_time_text: "Current Time",
    common_acc_note_text:
      "Note: When the half-screen mode is selected on the device side, the content below the dotted line will be covered.",
    common_password_inconsistency: "Inconsistent entry password",
    common_system_version: "Version",
    common_material_conversion_unable_view:
      "Material conversion failed, unable to preview",
    common_material_conversion_progress_unable_view:
      "Material conversion in progress, please wait",
    common_connot_lower_zero: "Cannot be lower than 0",
    common_company_type: "Please select a company type",
    // add
    common_user: "User",
    common_company: "Company",
    common_company_information: "Company information",
    common_company_switch: "Switching company",
    common_comapny_create: "Create company",
    // ADD1
    common_first_name: "First Name",
    common_last_name: "Last Name",
    common_email: "Email",
    common_input_first_name: "Please enter your first name",
    common_input_last_name: "Please enter your last name",
    common_input_mobile: "Please enter your telephone number",
    // add2
    common_agree_desc: "Please check the box and read the agreement",
    common_form_agree: "I have read and agree",
    common_user_agree: "《User Agreement》",
    common_and: "And",
    common_name_as: "Be named as",
    common_times_as: "The duration is",
    common_private_agree: "《Privacy Policy》",
    common_forgot: "Forgot?",
    common_form_username_input: "email address",
    common_form_password_input: "password",
    common_form_verification_input: "Verification code",
    common_form_username_placeholder: "Please enter code",
    common_form_password_placeholder: "Please enter password",
    common_form_code_placeholder: "Please enter the verification code",
    common_form_username_validation:
      "The mobile phone number or email address is mandatory",
    common_form_account_validation: "Account is required",
    common_form_password_validation: "The password is mandatory",
    common_form_area_validation: "The area code is mandatory",
    common_form_code_validation: "The verification code is mandatory",
    common_form_get_code: "Get Verification Code",
    common_form_get_code_surplus: " seconds left",
    common_form_user_code_validation:
      "The mobile phone number or email verification code is mandatory",

    // ADD3
    common_form_rule_msg: "The cell phone number or mailbox can not be empty",
    common_firstname_placeholder: "First Name",
    common_lastname_placeholder: "Last Name",
    common_op_batch_del: "Batch deletion",
    // add4
    common_check_data: "Please check the data you want to manipulate first",
    common_plese_select_file: "Please select the file",
    common_uploaded_wait: "Please wait while the file is uploaded",
    common_please_select_com: "Please Select",
    common_component_uoload_empty: "The upload file can not be empty",
    common_component_upload_max_length:
      "You cannot attach more than {{maxUploadFiles}} files",
    common_component_uoload_file_max_size:
      "The size of files cannot exceed {{maxFileSize}}Mb",
    common_component_uoload_file_extension:
      "Extension .{{extension}} has been excluded",
    // add5
    common_upload_remove_all: "Remove all",
    // ADD6
    common_add_program: "New programs",
    common_confirm_delete_program:
      "Are you sure you want to delete the program name: {{ids}} ?",
    // 保存修改
    common_save_change: "Save changes",
    common_forgort_title: "Forget your password",
    common_person_number_max: "More than 10000",
    common_company_name: "Company name",
    common_company_create: "Create a company organization",
    common_company_size: "Company size",
    common_label_company_type: "Type of company",
    common_login_select_company_size: "Please select company size",
    common_export_error: "Error exporting download file!",
    common_conn_server_error:
      "The back-end interface is improperly connected. Procedure",
    common_server_timeout: "The system interface request timed out",
    common_server_unknown_error: "System interface {{code}} is abnormal",
    common_confirm_delete_channel:
      "Do you want to delete a signage whose channel name is: {{ids}}?",
    common_confirm_delete_device:
      "Do you want to delete the signage whose name is {{ids}}?",
    common_confirm_delete_link_screen:
      "Confirm whether to delete the screen name as: {{ids}} ?",
    common_confirm_delete_merchant:
      "Do you want to delete the  Principal whose name is {{ids}} ?",
    common_confirm_link_delete_device:
      "Deleting {{ids}} digital tag will synchronously delete all playback plans in the tag. Are you sure to delete it?",
    common_confirm_link_delete_link_screen:
      "Deleting {{ids}} screen will synchronously delete all playback plans in that screen. Are you sure to delete?",
    common_confirm_reboot_device:
      "Confirm whether to restart the signage name is {{ids}} ?",
    common_confirm_sync_time_device:
      "Confirm whether to synchronize the signage at：{{ids}} ?",
    common_select_one_operation:
      "Only one object can be selected for operation",
    common_add_devops: "New Operator",
    common_add_retailer: "New Employee",
    common_add_advertiser: "New Employee",
    //todo
    common_add_retailerTitle: "New Employee of Principal",
    common_add_advertiserTitle: "New Employee of Advertiser",
    common_confirm_delete_location_type:
      "Do you want to delete the location type whose name is {{ids}} ?",
    common_confirm_delete_application:
      "Confirm whether to delete the application name as:{{ids}} ?",
    // 区域
    common_area_name: "Area Name",
    common_input_area_name: "Please enter a region name",
    common_area_all: "All Areas",
    common_belong_area: "Superior Area",
    common_area_accelerate: "Area Accelerate",
    common_select_area: "Please select an area",
    // 新增 2023/7/25
    common_table_query: "Query",
    common_form_unfold: "More",
    common_form_packup: "Retract",
    common_please_input_email: "Please enter your contact email",
    common_please_input_screen_number: "Please enter the number of screens",
    common_please_input_brand_name: "Please enter the Principal name",
    common_brand: "Principal",
    common_please_input_store_name: "Please enter the Outlet ",
    common_oper_edit_store: "Modify Outlet",
    common_mobile_area: "Area code",
    common_select_retail: " Select a Principal",
    common_please_select_retail: "Please select a Principal",
    common_form_area_name: "Area",
    common_store_location: "Store Location",
    common_store_total: "Store Total",
    common_store_name_notnull: "The Outlet cannot be empty",
    common_store_length_max:
      "The Outlet cannot exceed 100 characters in length",
    common_contact_name_notnull: "The contact cannot be empty",
    common_contact_length_max: "The contact name cannot exceed 30 characters",
    common_contact_phone_notnull: "The contact number cannot be empty",
    common_store_location_notnull: "The location of the Outlet cannot be empty",
    common_edit_sotre: "Modify Outlet Information",
    common_current_location_error: "Failed to get the current location",
    common_select_location: "Select Location",
    common_input_location_search: "Enter location information to search",
    common_search_info: "Search Information",
    common_location_e: "The location is",
    common_confirm_location: "Confirm the selection of the location",
    common_select_current_location:
      "The current selected city is {{address}},and the selected longitude and latitude are {{lng}}, {{lat}}.",
    common_current_location_e: "The current Outlet location is",
    common_current_lng_lat: "Latitude and longitude information",
    common_store_location_info: "Outlet Information",
    common_please_input: "Please enter",
    common_outlet_owner: "Outlet",
    common_location: "Location",
    common_add_screen: "Add Digital Signage",
    common_edit_screen: "Edit Signage",
    common_number_screen_list: "Digital signage list",
    common_screen_reboot: "Reboot",
    common_plese_select: "Please Select",
    common_show_screen_sn: "Please check the SN on the back of the machine",
    common_loading_select_outlet: "Select the outlet and then backfill",
    common_add_screen_alert_title:
      "Digital signage is energized and connected to the Internet",
    common_add_screen_alert_desc: `
    1.Enter the digital signage Ethernet Settings or WiFi Settings menu, enter the communication Settings page Network Settings successfully.
    <br />
    2.The server connection address is set, and the server is set successfully. <br />
    3.On the side of the digital signage box or the back of the digital signage, you can find the digital signage Sn.
    <br />
    4.Fill in the digital label Sn on the system.
    `,
    common_rule_device_name_length_max:
      "Enter a digital signage name less than 20 in length",
    common_plese_screen_sn: "Please enter the digital label SN",
    common_screen_sn_notnull: "Digital label SN cannot be empty",
    common_screen_sn_length_max: "Digital label SN cannot be empty",
    common_plese_screen_direction: "Please select digital signage directions",
    common_plese_scrren_onwer_outlet: "Please select your Outlet",
    common_screen_detail: "Digital signage Details",
    common_link_screen_name: "Associated Signage Name",
    common_link_screen_list: "Associated signage list",
    common_link_screen_num: "Digital Signage Qty.",
    common_link_screen_specifca: "Specification",
    common_Unable_load: "Unable to load",
    common_link_column: "Column",
    common_link_row: "Line",
    common_link_add: "Add Screen",
    common_confirm_delete_store: `Are you sure you want to delete the data entry for outlet with name "{{names}}"?`,
    common_error_email: "Please enter a valid email address.",
    common_error_phone: "Please enter a valid phone number.",
    common_error_phone_area: "Please enter a valid mobile phone area code.",
    common_upload_type_not_support: "The current type is not supported",
    common_upload_file_tips: `Maximum number of uploads :6 </br> Maximum file size :1GB</br>Supported formats for resources include :`,

    // 8/25
    common_upload_all_message_success:
      "Upload of all files completed successfully",
    common_drage_branch_file: "Drag or browse",
    common_add_create_advertiser: "Add Advertiser",
    common_add_create_retailer: "Add Principal",
    common_select_brand: "Please select an brand",
    common_advertiser_name: "Advertiser",
    common_retailer_name: "Principal Name",
    common_please_retailer_name: "Please Principal name",
    common_please_advertiser_name: "Please Advertiser name",
    common_upload_name_exist:
      "File name: {{name}}, Already exists, Cannot upload",
    common_upload_process: "Upload Process",
    common_Background_upload: "Background Upload",
    common_login_account: "Login Account",
    common_login_user_name: "Login User Name",
    common_login_ip: "Login IP",
    common_login_address: "Login Address",
    common_access_time: "Access Time",
    common_login_message: "Login Message",
    common_login_status: "Status",
    common_operation: "Operation",
    common_login_success: "Successful login",
    common_login_fail: "Failed login",

    // 8/28
    common_upload_image_tips: `Limit number of files to upload: 1<br>Files cannot exceed 100MB in size<br>Compatible file formats`,

    common_cover_upload: "Cover upload",
    common_select_image: "Select image",
    common_upload_file_size_max:
      "File size exceeds limit: {{fileSize}}MB. Please upload files that are smaller than or equal to 100MB.",
    common_upload_file_size_max_1G:
      "File size exceeds limit: {{fileSize}}MB. Please upload files that are smaller than or equal to 1 GB.",
    common_operation_method: "Operation Method",
    common_operation_account: "Operation Account",
    common_operation_IP: "Operation IP",
    common_operation_address: "Operation Address",
    common_operation_time: "Operation Time",
    //8/29
    common_system_resoltion_recommond:
      "Suggested screen resolution: 1920x1080 or higher", common_system_resoltion_recommond:
      "Suggested screen resolution: 1920x1080 or higher",
    common_store_space: "System Storage Space",
    common_system_flow: "System Flow",
    common_security_settings: "Security Settings",
    common_use_total: "used {{use}}GB / total {{total}}GB",
    common_change_avatar: "Change avatar",
    common_tailor: "Tailor",
    common_zoom: "Enlarge",
    common_reduce: "Narrow",

    common_account_tips: "Please input Mobile phone number or email address",
    common_please_change_password:
      "For account security, please change the initial password",
    common_time_not_repeat_annotation:
      "Note: Cannot set a repeating time period.",
    common_duraation_annotation:
      "Note: If the set playback duration is greater than the video duration, the carousel will be played, and if the video duration is less than the video duration, the playback will be interrupted.",
    common_set_duration: "Set playback duration",
    common_area_name_not_null: "The area cannot be empty",
    common_input_sort: "Please input the Volume",

    //9/7
    common_clear_operation: "Clear Operation",
    common_warning_prompt: "Warning prompt:",
    common_data_loss_risk: "Data loss risk:",
    common_op_warn_text_1:
      "Risk of data loss: Deleting operation logs permanently deletes all recorded operation history, including modification and other critical operations. Once emptied, the log data cannot be recovered.",
    common_incomplete_audit_trail: "Incomplete audit trail:",
    common_op_warn_text_2:
      "Operation logs are an important audit tool for tracking and tracing events that occur in the system. Clearing operation logs may result in an incomplete audit trail, making subsequent security investigations and troubleshooting more difficult.",
    common_login_warn_text_1:
      "Clearing login logs permanently deletes all recorded login histories, including key information such as login time and IP address. Once emptied, the log data cannot be recovered.",
    common_security_investigation_difficult:
      "Security investigation is difficult:",
    common_login_warn_text_2:
      "Login logs are an important security tool used to monitor and detect potential security threats and unauthorized access. Clearing login logs may make subsequent security checks and investigations difficult, making it impossible to trace and analyze potential security incidents.",

    // 9/8
    common_content_name: "Content Name",
    common_select_schedule: "Select Schedule Type",
    common_start_date: "Start Date",
    common_end_date: "End Date",
    common_content_source: "Source of content",
    common_account_not_null: "Account cannot be empty",
    common_enter_account_phone: "Please enter your account(email)",
    common_only_file_apk: "Only allow uploading files in apk format",
    common_select_package_type: "Select Upgrade Package Type",
    common_upload_upgrade_package: "Upload upgrade package and upgrade",
    common_upgrade: "Upgrade",
    common_batch_upgrade: "Batch upgrade",
    common_enter_package_version:
      "Please enter the upgrade package version, starting with the letter 'V'",
    common_enter_limit_start_v: "Must start with the letter V",

    // 11/6
    common_area_center_location: "Region center point location",
    common_please_area_center_location:
      " Please select the region center point location.",
    common_delete_confirm_description: "Please confirm whether to delete!",
    common_name_year: "Year",
    common_name_month: "Month",
    common_please_enter_year: "Please enter the year",
    common_please_enter_month: "Please enter the month",
    common_los_merchant_name: "Affiliated Principal",
    common_los_area_name: "Owning region",
    common_year_and_month: "Year and month",
    common_total: "Totality",
    common_btn_add_outlet: "Add Outlet Groups",
    common_title_add: "Add Outlet Groups",
    common_title_edit: "Edit Total Outlet",
    common_please_year_and_month: "Please select the year and month",
    common_total_min_0: "The total cannot be less than 0",
    common_total_please: "Please enter total",
    common_merchant_please_logo: "Please upload the brand identity",
    common_merchant_logo: "Brand identity",
    common_file_max_size: "File size must not exceed, {{size}}",
    common_please_device_Alias: "Please enter a digital signage alias",
    common_export_type: "Export type",
    common_export_type_one: "Export current page data",
    common_export_type_zero: "Export all data",
    common_export_field: "Export fields",
    common_export_tips:
      "Tips: If not selected, all fields will be exported by default",
    common_export_btn: "Export",
    common_export_screen_title: "Export Digital Signage data",
    common_export_store_title: "Export Outlet data",
    common_export_sub_record_title: "Export subscription record data",
    // xinzeng
    common_download_error: "Download failure",
    common_import_error: "Import failure",
    common_import_template_name: "Outlet Import Template",
    common_import_store_title: "Import Outlet Data",
    common_import_type: "Import Type",
    common_import_type_one: "Overwrite Existing Data",
    common_import_type_two: "Do Not Overwrite Existing Data",
    common_import_extends1_title: "Overwrite Existing Data:",
    common_import_extends1_desc:
      "If the Outlet name already exists, it will be updated; if not, it will be saved.",
    common_import_extends2_title: "Do Not Overwrite Existing Data:",
    common_import_extends2_desc:
      "If there are duplicate Outlet names, the import will fail.",
    common_import_upload: "Upload File",
    common_download_template_tips:
      "Please update the template before each import.",
    common_download_template_btn: "Click to Download Template",
    common_import_tips1: "Please follow the table rules for importing.",
    common_import_tips2: "The format for import should be .xlsx.",
    common_import_tips3: "Try not to exceed 500 records for a single import.",
    common_import_next: "Next",
    common_import_receipt: "Import Receipt",
    common_import_success_num: "Number of Successful Imports: {{num}} records",
    common_import_error_num: "Number of Failed Imports: {{num}} records",
    common_import_loading: "Importing Data",
    common_import_loading_desc: "Importing data... Please wait patiently.",
    common_import_loading_desc2:
      "For large datasets, the import process may take longer.",
    common_layout: "layout",

    common_play_list_type_layout: "Layout List",
    common_add_layout_resource: "Layout Resources",
    common_not_add_layout_resource:
      "Layout resources and video images cannot coexist at the same time",

    //1/19
    common_copy_playlist_tips:
      "Confirm to overwrite and copy the imported playlist!",
    common_advanced_menu_title: "Advanced Features",
    common_input_interval_time: "Please enter a custom interval time",
    common_copy_row: "Copy selected rows",
    common_copy_row_null_playlist:
      "The selected copy contains empty playlists or playback times",
    common_please_select_row_copy: "Please select the rows to be copied",
    common_please_add_new_row: "Add a new row",
    common_copy_input_info: "Copy input information",
    common_copy_tips_info:
      "Copy based on the last one, and increment the playback schedule according to the selected time interval.",
    common_interval_time: "Time interval",
    common_interval_time_15_minute: "15 min",
    common_interval_time_30_minute: "30 min",
    common_interval_time_60_minute: "60 min",
    common_interval_time_customer_minute: "Custom unit/min",
    common_duration_minute: " Duration/min",
    common_copy_ok: "Confirm Copy",
    common_screen_shot_detail: "Screenshot details",
    common_screen_base_info: "Basic information",
    common_screen_shot_time: "Screenshot time",
    common_screen_copy_del_error: "Select the Row that you want to delete",
    common_screen_shot_preview_thumbnail: "Thumbnail",
    common_copy_erro_null: "Failed to import if the list is empty",
    common_screen_colour_temperature: "Device Color Temperature",
    common_screen_base_tab: "Basic Configuration",
    common_screen_controller_tab: "Device Control",
    common_free: "Free",
    common_package_price: "Product pricing information",
    common_package_details: "Package Details",
    common_remaining_day: "Remaining Valid Days (days)",
    common_no_limit: "No Limit",
    common_used_day: "Used: {{day}} days",
    common_gross: "Total",
    common_day: "days",
    common_remaining_device_count: "Remaining Device Count (units)",
    common_add_device_num: "Added: {{count}} units",
    common_add_device_dot: "units",
    common_deviceType: "Device type",
    common_please_type: "Please select the device type",

    common_table_count: "Total:{{total}} items",
    common_publish_scheduleName:
      "The plan name for one-click delivery is as follows:",
    common_schedule_copy: "Copy",
    common_copy_schedule_playList: "Copy Playlist Schedule",
    common_start_date_end_date: "Start Date - End Date",
    common_please_screen_store: "Select Device Store",
    common_select_rule_screen:
      "Please select screens with the same specifications as the copy list",
    common_schedule_publish_btn: "Publish",
    common_copy_text: "- Copy",
    common_material_category: "Material Group",
    common_material_category_please: "Please select a material group",
    common_empty_select: "No data available",
    common_material_category_button: "Material Group",
    common_material_category_name: "Group Name",
    common_material_category_name_please: "Please enter the group name",
    common_material_category_table_column_name: "Group Name",
    common_material_category_table_column_merchant: "Principal",
    common_material_category_table_column_sort: "Sort",
    common_material_category_add_button: "Add Group",
    common_material_category_add_title: "Add Group",
    common_material_category_edit_title: "Edit Group",
    common_material_remove_one_tips: "Are you sure you want to delete the group '{{name}}'? This action cannot be undone",
    common_material_remove_more_tips: "Are you sure you want to delete these groups '{{names}}'? This action cannot be undone",
    common_material_category_name_length_rule: "Group name must be between 1 and 50 characters long",
    common_oneclick_description_tips: "The possible causes of the publishing failure are: 1. The device is offline; 2. The IOT connection is incorrect.",
    common_parent_material_group: "Parent Material Group",
    common_parent_material_group_please: "Please select the parent material group",
    common_parent_material_group_tips: "Note: Maximum two levels of groups can be added",
    back: "Back",
    common_material_category_name_lenth_rule: "Material group name must be between 1 and 50 characters long",
  },
  //ips
  ips: {
    ips_template_1: "Company Profile",
    ips_template_2: "New product conference_ has real objects",
    ips_template_3: "New product release_nonless object",
    ips_template_4: "Technology electronics",
    ips_template_5: "Summit",
    ips_template_6: "Exchange Forum",
    ips_template_7: "Exchange",
    ips_template_8: "Medical exchange",
    ips_template_9: "Doctor introduce",
    ips_template_10: "Luxury home",
    ips_template_11: "clothing",
    ips_template_12: "Education and Training Message Notice",
    ips_template_13: "New Notice of Education and Training Recruitment",
    ips_template_14: "Education and training_ graduation theme",
    ips_template_15: "Special event",
    ips_template_16: "Food",
    ips_template_17: "Cake class",
    ips_template_18: "Drink",
    ips_template_19: "Menu style 01",
    ips_template_20: "Menu style 02",
    ips_material: "Material Name",
    ips_material_code: "Material Number",
    ips_material_time: "Duration/Pages",
    ips_material_upload: "Upload Material",
    ips_advertising_resources_upload: "Upload ADs",
    ips_material_group: "Material Grouping",
    ips_material_select: "Select resources",
    ips_material_add: "Add Material",
    ips_material_edit: "Modify Material",
    ips_material_del:
      'Are you sure to delete the data with material number "{{id}}"?',
    ips_material_audit: "Are you sure you want to audit this material?",
    ips_select_auditor: "Please select the auditor",
    ips_auditor_person: "auditor",
    ips_program: "Program",
    ips_program_code: "Program Code",
    ips_program_scene: "Program Scene",
    ips_program_scene_num: "Number of Scenes",
    ips_program_type: "Program Type",
    ips_programDuration: "Program Duration",
    ips_program_durations: "Duration(s)",
    ips_program_duration: "Duration(s)",
    ips_program_durationss: "Durarion/s",
    ips_program_add: "Add a Program",
    ips_program_edit: "Modify a Program",
    ips_program_export_list: "Export the program list",
    ips_select_to_location:
      "Are you sure you want to export all the show data?",
    ips_program_export_data: "Export the program data",
    ips_program_export: "Are you sure to export all program data?",
    ips_programData_export: "Are you sure to export the selected program data?",
    ips_program_del: 'Are you sure to delete the program number"{{id}}"?',
    ips_template: "Template Name",
    ips_template_add: "Add a Template",
    ips_template_edit: "Modify a Template",
    ips_template_del:
      'Are you sure to delete the data with the template number"{{id}}"?',
    ips_template_export: "Are you sure to export all template data?",
    ips_program_scheduling: "Program Scheduling",
    ips_scheduling: "Schedule Name",
    ips_scheduling_code: "Schedule Code",
    ips_scheduling_audit: "Schedule for Editing",
    ips_scheduling_type: "Schedule Type",
    ips_scheduling_playMode: "Play Mode",
    ips_scheduling_local: "Local Play",
    ips_scheduling_online: "Play Online",
    ips_scheduling_playWeeks: "Repetition Mode",
    ips_scheduling_playDate: "Play Date",
    ips_scheduling_playTime: "Start Date-End Date",
    ips_scheduling_downloadTime: "Download Time",
    ips_scheduling_info: "Basic Information",
    ips_scheduling_program: "Select Program",
    ips_scheduling_device: "Select Device",
    ips_scheduling_add: "Add a Schedule",
    ips_scheduling_edit: "Modify a Schedule",
    ips_scheduling_del:
      'Deleting the playback plan will synchronously delete device data. Are you sure you want to delete the data with the playback plan name "{{id}}"?',
    ips_scheduling_publish:
      'Are you sure you want to publish a schedule named "{{id}}"?',
    ips_scheduling_export: "Are you sure to export all schedule data?",
    ips_caption_info: "Insert a message",
    ips_caption_info_audit: "Edit the Plug-in message",
    ips_caption_title: "Insert a Title",
    ips_caption_content: "Insert Content",
    ips_caption_position: "Insert a Location",
    ips_caption_mode: "Caption Mode",
    ips_caption_period: "On Time Period",
    ips_caption_timeLen: "On Instant Time",
    ips_caption_add: "Add a caption",
    ips_caption_edit: "Modify a caption",
    ips_caption_del:
      'Are you sure to delete the data with the insert message number"{{id}}"?',
    ips_caption_export: "Are you sure to export all insert data?",
    ips_device: "Digital Signage Name",
    ips_device_type: "Device Type",
    ips_deviceList: "Device List",
    ips_device_add: "Add a device",
    ips_device_edit: "Modify a device",
    ips_device_id: "Device Id",
    ips_device_code: "Device Code",
    ips_device_ip: "IP Address",
    ips_device_address: "Location",
    ips_device_status: "Device Status",
    ips_device_online: "Online Status",
    ips_device_softVersion: "Software Version",
    ips_device_auth: "Device Certification",
    ips_device_info: "Device Information",
    ips_device_overview: "Device Overview",
    ips_device_volume: "Device Volume",
    ips_device_curProgram: "Current Program",
    ips_device_runTime: "Device Operation Time",
    ips_device_memory: "Device Memory",
    ips_device_storage: "Device Storage Space",
    ips_device_scheduling: "Current Schedule",
    ips_device_checkTime: "Release Time",
    ips_device_screen: "Capture",
    ips_device_capture: "Screen Capture",
    ips_device_del:
      'Are you sure to delete the data with the device number"{{id}}"?',
    ips_device_export: "Are you sure to export all the device data?",
    ips_device_modeSet: "Are you sure to set the mode to the Cloud mode?",
    ips_device_clear: "Are you sure to clear the selected device data?",
    ips_device_mute: "Are you sure to mute the selected device?",
    ips_device_shutDown: "Are you sure to turn off the selected device?",
    ips_device_reboot: "Are you sure to restart the selected device?",
    ips_device_playStart:
      "Are you sure to start playing videos on the selected device?",
    ips_device_playStop:
      "Are you sure to stop playing videos on the selected device?",
    ips_device_screenShot:
      "Are you sure to capture the selected device screen?",
    ips_device_upgrade:
      "Are you sure to upgrade the selected device application version?",
    ips_device_backLight: "Backlight Settings (0 means Off, 1 means On)",
    ips_device_brightness: "Please input the brightness(0 to 255).",
    ips_device_screenSet:
      "Horizontal and Vertical Screen Settings (0 means Horizontal, 1 means Vertical)",
    ips_material_landscape: "Horizontal screen",
    ips_material_portrait_screen: "Vertical screen",
    ips_download_status: "Download Status",
    ips_download_times: "Number of Issuance",
    ips_download_progress: "Download Progress",
    ips_download_resend: "Re-issue",
    ips_playRecord_times: "Playback Times",
    ips_playRecord_detail: "Playback Details",
    ips_playRecord_del:
      'Are you sure to delete the playback record number"{{id}}"?',
    ips_resend_issue: 'Are you sure to resend the data numbered"{{id}}"?',
    ips_playRecord_export: "Are you sure to export all playback records?",
    ips_version: "Version Name",
    ips_version_code: "Version Number",
    ips_version_type: "Version Type",
    ips_version_appSize: "Version Size",
    ips_version_appDesc: "Description",
    ips_version_add: "Add a Version",
    ips_version_edit: "Modified Version",
    ips_version_del:
      'Are you sure to delete data with the version number"{{id}}"?',
    ips_version_export: "Are you sure to export all version data?",
    ips_appLog: "Log Title",
    ips_appLog_type: "Log Type",
    ips_appLog_info: "Log Information",
    ips_appLog_error: "Error Log",
    ips_appLog_message: "Log messages",
    ips_appLog_log: "Pulling logs",
    ips_appLog_pull_log:
      "Are you sure you want to pull the logs for this device?",
    ips_appLog_del:
      'Are you sure to delete data with the device application log number"{{id}}"?',
    ips_appLog_export: "Are you sure to export all device application logs?",

    ips_group_del: 'Are you sure to delete the Group"{{name}}"?',
    ips_group_exist: "The group name already exists!",
    ips_group_not_exist: "The group name does not exist!",

    ips_dept: "Organization",
    ips_store_name: "Outlet Name",
    ips_store_brand: "Principal",
    ips_store_location: "Position",
    ips_store_screen_number: "Digital Signage Qty.",
    ips_store_contacts: "Contact",
    ips_store_phone: "Mobile",
    ips_store_email: "Email",
    ips_store_address: "Address",
    ips_area_del: 'Are you sure you want to delete the "{{name}}" area?',
    ips_area_name: "Jurisdictional Area",
    ips_add_store: "New Outlet",
    ips_stay_area_name: "Area",
    // 2023/07/26
    ips_device_sn: "SN",
    ips_screen_direction: "Direction",
    ips_fwversion: "Firmware",
    ips_timezone: "TimeZone",
    ips_screen_alias: "Alias",
    ips_screen_brightnes: "Luminance",

    ips_store_client: "Principal",
    ips_store_outlet_name: "Outlet Name",
    ips_store_outlet_type: "Outlet Type",
    ips_store_outlet: "Outlet",
    ips_store_country: "Country",
    ips_store_province: "State/Province",
    ips_store_city: "City",
    ips_store_city_zone: "City Zone",
    ips_store_time_zone: "Time Zone",
    ips_store_client_name: "Principal",
    ips_store_region: "Region",
    ips_store_tips_select_country: "Please select a country",
    ips_store_tips_select_client: "Please select a Principal",
    ips_store_tips_select_province: "Please select a state/province",
    ips_store_tips_select_city: "Please select a city",
    ips_store_tips_select_outlet_type: "Please select a outlet type",
    ips_store_tips_select_city_zone: "Please select a city zone",
    ips_store_tips_select_outlet: "Please select a outlet",
    ips_status_monitoring: "Signage Status Monitoring",
    ips_signage_number: "Signage Quantity",
    ips_online_number: "Online Quantity",
    ips_offline_number: "Offline Quantity",
    ips_outlet_number: "Outlet Quantity",
    ips_online_count: "Online frequency",
    ips_offline_count: "Offline frequency",
    ips_offline_rate: "Offline Rate",
    ips_offline_online_count: "Offline frequency/Online frequency",
    ips_last_online_time: "Last Online Time",
    ips_resource_proportion: "Proportion",
    ips_total_resources: "Total Resources",
    ips_picture: "Picture",
    ips_media: "Video",
    ips_aduio: "Audio",
    ips_total: "Total",
    ips_traffic_statistics: "Traffic Statistics",
    ips_traffic_time: "Traffic/Time",
    ips_total_usage_traffic: "Personal used traffic",
    ips_merchant_name: "Principal Name",
    ips_merchant_type: "Principal Type",
    ips_type: "Type",
    ips_resolution: "Resolution",
    ips_resolution_wide: "Resolution Width",
    ips_resolution_high: "Resolution Height",
    ips_resolution_input_number: "Please enter a number",
    ips_playlist_name: "Playlist Name",
    ips_selected_advertisements: "Selected Contents",
    ips_total_duration: "Total duration",
    ips_playlist_type: "Playlist Type",
    ips_new_playlist: "New Playlist",
    ips_editor_playlist: "Editor Playlist",
    ips_merchant: "Principal",
    ips_signage_name: "Signage Name",
    ips_playing: "Playing",
    ips_real_playback: "Real time playback data",
    ips_playlist_list: "Create Playlist List",
    ips_meida_image: "Video/Image",
    ips_digital_signage: "Digital signage schedule",
    ips_new_digital_signage: "New Digital Signage Schedule",
    ips_first_step: "1.Select a digital signage",
    ips_second_step: "2.Set basic information",
    ips_select_playlist: "Select Playlist",
    ips_please_select_playlist: "Please select a Playlist",
    ips_please_select_playlist_name: "Please select a Playlist Name",
    ips_select_month: "Please select a month",
    ips_select_date: "Please select a date",
    ips_playlist_exist: "List name or play time not selected",
    ips_schdule_publishing:
      "The schedule is currently being distributed, please do not click repeatedly",
    ips_at_least_one: "Please select at least one digital sign",
    ips_no_select_offline: "Offline digital signage cannot be selected",
    ips_starttime_greater_endtime:
      "End time is less than or equal to start time, please reselect",
    ips_overlap_play_time:
      "There is overlap in the playback time period, please reselect",
    ips_exceeds_one_year:
      "Please choose again if the time span exceeds one year",
    ips_all: "All",
    ips_month: "Month",
    ips_date: "Date",
    ips_screen_name: "Signage Name",
    ips_schedule_type: "Schedule Type",
    ips_startDate_endDate: "Start Date-End Date",
    ips_schedule_list: "PlayList",
    ips_please_select_shcedule_type: "Please select a schedule type",
    ips_signage_schedule: "Digital Signage Schedule",
    ips_link_schedule: "Link Schedule",
    ips_play_link_schedule: "Associated signage schedule",
    ips_new_link_schedule: "Add Associated Signage Schedule",
    ips_first_select_link: "1.Select a associated signage",
    ips_screen_group: "Screen group",
    ips_at_least_one_group: "Please select at least one screen group",
    ips_select_same_specifications:
      "Please select a linked signage of the same specification",
    ips_select_contain_offline:
      "The selected screen group contains offline signage",
    ips_reset: "reset",
    ips_add_playlist: "Add PlayList",
    ips_add_new_playlist: "New PlayList",
    ips_screen_specifications: "Screen specifications",
    ips_please_select_direction: "Please select a direction",
    ips_playlist_select: "Playlist Selection",
    ips_preview_time: "Playlist duration preview",
    ips_select_playlist_direction:
      "Please select the direction of the playlist",
    ips_select_playlist_type: "Please select a playlist type",
    ips_enter_playlist_name: "Please enter a playlist name",
    ips_add_media_iamge: "Add videos/images",
    ips_add_media_music: "Add background music",
    ips_add_resource: "New Resources",
    ips_append_playlist_list: "Append Playlist",
    ips_new_area: "New Area",
    ips_select_status: "Please select a status",
    ips_new_merchant: "New  Principal",
    ips_select_merchant: "Please select Principal",
    ips_select_merchant_type: "Please select  Principal type",
    ips_select_Principal_client: "Please select  Principal Client",
    ips_select_audit_status: "Please select the audit status",
    ips_edit_merchant: "Edit  Principal",
    ips_login_password: "Login Password",
    ips_sure_password: "Confirm Password",
    ips_belong_retail: "Principal",
    ips_belong_advertiser: "Advertiser",
    ips_enter_surname: "Please enter the user's last name",
    ips_enter_name: "Please enter the user's first name",
    ips_enter_email: "Please enter your email address",
    ips_enter_areacode: "Please enter the area code",
    ips_enter_phone: "Please enter your phone number",
    ips_enter_position: "Please enter the position",
    ips_enter_region: "Please select an area",
    ips_enter_password: "Please enter the password",
    ips_enter_password_again: "Please re-enter the login password",
    ips_select_retail: "Please select a Principal",
    ips_select_advertiser: "Please select a advertiser",
    ips_selected_outlet_list: "Selected Outlet list",
    ips_allocation_outlet: "Assign Outlet",
    ips_select_outlet: "Outlet Selection",
    ips_select_jurisdiction: "Select an area",
    ips_duration_is: "duration is",
    ips_name_is: "name is",
    ips_enter_playback_duration: "Please enter the playback duration",
    ips_duration_greater: "Duration greater than 1 second",
    ips_exist_media_audio: "Video and audio cannot coexist",
    ips_set_duration: "Set playback duration",
    ips_order: "Order",
    ips_edit_playlist: "Edit Playlist",
    // add
    ips_screen_list: "Screen PlayList",
    ips_link_screen_list: "LinkScreen PlayList",
    ips_playList_count_duration: "Playlist Duration",
    ips_enter_merchant_name: "Please enter the  Principal name",
    ips_enter_location_name: "Please enter the location type name",
    ips_add_location_type: "New Location Type",
    ips_select_location_type: "Please select the location type",
    ips_location_type: "Location Type",
    ips_location_type_name: "Location type name",
    ips_edit_location_type: "Edit Location Type",
    ips_digital_signage_schdule: "Digital signage schedule",
    ips_link_signage_schdule: "Linked screen schedule",
    ips_phone: "Mobile",
    ips_last_name_null: "Last name cannot be empty",
    ips_last_name_less30: "Last name length should be less than 30",
    ips_first_name_null: "First name cannot be empty",
    ips_first_name_less30: "First name length should be less than 30",
    ips_need_email_or_phone: "Either phone number or email is required",
    ips_email_error: "Invalid email address",
    ips_phone_error: "Invalid phone number",
    ips_phone_code_error: "Invalid phone area code",
    ips_length_less30: "Length should be between 6 and 30 characters",
    ips_password_different: "Passwords do not match",
    ips_enter_outlet_address: "Please enter the outlet address",
    ips_edit_pricipal: "Edit principal client user",
    ips_edit_devops: "Edit operator user",
    ips_edit_retail: "Edit Principal user",
    ips_not_select_single: "Not allowed to select 1*1",
    ips_not_reSelect_signage: "Cannot select the same digital signage again",
    ips_full_all_signage: "Please fill all the digital signage blocks",
    ips_enter_link_name: "Please enter the associated signage name",
    ips_link_name_null: "Link name cannot be empty",
    ips_link_name_lenth20: "Link name length should be less than 20",
    ips_select_a_outlet: "Please select an outlet",
    ips_select_rows: "Please select the number of rows",
    ips_select_colmuns: "Please select the number of columns",
    ips_select_loaction_to: "Select Loaction",
    ips_link_specifications: "Specifications (rows * columns)",
    ips_click_signage_bind:
      "Click on the digital signage block to select the binding digital signage",
    ips_signage_select: "Select Digital Signage",
    ips_not_select_outlet: "No outlet selected",
    ips_not_select_colmun_row: "No rows or columns selected",
    ips_offline_not_select: "Offline digital signage cannot be selected",
    ips_enter_location: "Please enter the location",
    ips_total_storage: "Total Storage",
    ips_sys_total_storage: "System Storage",
    ips_sys_used_storage: "System Used",
    ips_personal_data: "Personal Data",
    ips_self_use: "Personal Used",
    ips_system_last: "System Remain",
    ips_region: "Region",
    ips_total_number: "Total Number",
    ips_current_time_no_play: "No Schedule for Current Time Slot",
    ips_in_week: "Week",
    ips_in_month: "Month",
    ips_in_year: "Year",
    ips_sub_month: "Subscription Month",
    ips_sub_year: "Subscription Term",
    ips_resource_percent: "Person storage",
    ips_sys_used_storage_percent: "System Used",
    ips_total_outlet_num: "Outlet Number",
    ips_total_signage_num: "Signage Number",
    ips_outlet_address: "Outlet Address",
    ips_operator_name: "Operator Name",
    ips_operator_email: "Operator Email",
    ips_select_province: "Select Province",
    ips_select_region_county: "Select Region/County",
    ips_playlist_play_time: "Playlist Time",
    ips_schedule_detail: "Schedule Detail",
    ips_current_playlist: "Current Playlist",
    ips_link_schedule_detail: "Linked Schedule Detail",
    ips_digital_signage_name: "Digital Signage Name",
    ips_select_signage_status: "Select Signage Status",
    ips_enter_location_address: "Address",
    ips_system_used: "System Used",
    ips_screen_model: "Model",
    ips_screen_app_version: "App Version",
    ips_app_prod_version: "Official Version",
    ips_app_dev_version: "Beta Version",
    ips_publishing: "Publishing",
    ips_publish_fail: "Publish fail",
    ips_publish_finish: "Published",
    ips_no_publish: "Unpublished",
    ips_downloading: "Downloading",
    ips_wait_download: "To be downloaded",
    ips_download_fail: "Download failed",
    ips_download_success: "Download successful",
    ips_schedule_play_status: "Playback status",
    ips_schedule_LCD_L101_valid: "[Please note] The device's memory have selected is small, please check these limitations: 1. Only can synchronize one playlist to it. 2. The size of materials in the playlist cannot exceed 60MB.",
    ips_schedule_LCD_L101_Invalid: "Operation successful. Because of the device's memory have selected is small. After issuing a new playback plan, device existing playback plan will become invalid.",
    ips_oneClick_schedule_L101_valid: "If there is a device with limited memory in the pushed playback schedule, the existing playback plan will become invalid.",
    ips_sync_store_timezone: "Synchronize Time Zone",
    ips_confirm_sync_store_timezone: "Are you sure you want to synchronize the store time zone?",
    ips_select_site_timezone: "Please select the location and time zone",
    ips_timezone_device_setting: "Based on device settings",
  },
  //字典
  dictData: {
    dict_convert: "Processing",
    dict_pend_audit: "Pending for Audit",
    dict_audit_pass: "Approved",
    dict_audit_not_pass: "Not Approved",
    dict_convert_success: "Upload Successful",
    dict_convert_failed: "Upload Failed",
    dict_not_complete: "Incomplete Resources",
    dict_ordinary_program: "Ordinary Program",
    dict_car_program: "Car Program",
    dict_acc_program: "Access Program",
    dict_normal: "Normal",
    dict_stop: "Stop",
    dict_auth_status_modeSwitch: "Mode Switch Request",
    dict_auth_status_unverified: "Not Yet Verified",
    dict_issue: "Issued",
    dict_unIssue: "Not Issued",
    dict_android: "Android",
    dict_other: "Other",
    dict_success: "Successful",
    dict_failed: "Failed",
    dict_login_type: "Login Type",
    dict_login_type_app: "App Abnormal",
    dict_login_type_login: "Login",
    dict_login_type_logout: "Logout",
    dict_device_type_ad: "Advertising Box",
    dict_device_type_outDoorAd: "Outdoor Advertising Machine",
    dict_company_industry_it: "IT service",
    dict_company_industry_make: "Manufacturing",
    dict_company_industry_life: "Life service",
    dict_company_industry_school: "School education",
    dict_company_industry_government: "government",
    dict_company_industry_finance: "finance",
    dict_company_industry_entertainment: "Place of entertainment",
    dict_company_industry_medical: "Medical treatment",
    dict_company_industry_other: "Other industries",
  },
  //菜单
  menu: {
    adverStatistics: "Advertising statistics",
    media_permission: "User permissions",
    media_total_outlet_manager: "Total Outlet",
    media_dashboard: "Dashboard",
    summary_dashboard: "Summary",
    map_dashboard: "Map",
    editor: "editor",
    outlet_dashboard: "Outlet",
    media_retailer_dashboard: "Principal board",
    media_advertiser_dashboard: "Principal client board",
    media_program: "Program Management",
    media_material: "Material Management",
    media_location_type_manager: "Location Type",
    media_program_production: "Program Creation",
    media_advertising_resources: "Content Resource",
    media_template: "Template Management",
    media_information_publish: "Schedule",
    media_scheduling: "Schedule",
    media_resdload: "Program Status",
    media_play_record: "Playback Statistics",
    media_device: "Device Management",
    media_device_list: "Device List",
    media_version: "Device Version",
    media_deviceLog: "Device Log",
    media_system: "System Management",
    media_dept: "Department Management",
    media_role: "Role Management",
    media_user: "User Management",
    media_menu: "Menu Management",
    media_dict: "Dictionary Management",
    media_config: "Parameter Management",
    media_shortcut: "Shortcut",
    media_group: "Group Management",
    media_area: "Area Management",
    media_tenant: "Tenant Management",
    media_backup: "Data Backup",
    media_monitor: "Operation Overview",
    media_job: "Timed Tasks",
    media_data_monitor: "Data Monitoring",
    media_server_monitor: "Service Monitoring",
    media_loginLog: "Login Log",
    media_screen_shot: "ScreenShot",
    media_operationLog: "Operation Log",
    media_log_overview: "Log Overview",
    media_screen_offline_stat: "Screen Offline Statistics",
    media_monitor_overview: "Operation Management",
    media_personal_center: "Personal Center",
    media_personal_about: "About",
    media_dict_data: "Dictionary Data",
    media_merchant_manager: "Principal Management",
    esdc_crossline_config: "Line crossing configuration",
    esdc_device_manager: "Equipment management",
    esdc_device: "equipment",
    log_manager: "Log management",
    oper_log: "Operation record",
    login_log: "Login record",
    media_store: "Outlet Management",
    media_store_list: "Outlet List",
    media_devops_list: "IT Operation Management",
    media_retailer_list: "Principal Management",
    media_advertiser_list: "Principal Client Management",
    media_login: "Login",
    media_403: "Insufficient Permissions",
    media_404: "Page Not Found",
    media_area_page: "Area Management",
    media_marchant: "Principal Management",
    media_schedule_push: "Play Schedule",
    media_schedule_add_common: "Add Digital Signage Schedule",
    media_schedule_add_link: "Add Linked Screen Schedule",
    media_schedule_edit_common: "Edit Digital Signage Schedule",
    media_schedule_edit_link: "Edit Linked Screen Schedule",
    media_screen_add: "Add Digital Signage",
    media_screen_edit: "Edit Digital Signage",
    media_screen_details: "Digital Signage Details",
    media_link_screen: "Linked Screens List",
    media_link_screen_add: "Add Linked Screen",
    media_link_screen_edit: "Edit Linked Screen",
    media_application_page: "Application Management",
    //操作按钮
    button_add_dept: "Add an Organization",
    button_update_dept: "Modify the Organization",
    button_del_dept: "Delete the Organization",
    button_query_dept: "Query about an organization",
    button_import_dept: "Import an Organization",
    button_add_role: "Add a Role",
    button_update_role: "Modify a Role",
    button_del_role: "Delete a Role",
    button_query_role: "Query about a Role",
    button_role_data: "Data Permission",
    button_add_user: "Add a User",
    button_update_user: "Modify a User",
    button_del_user: "Delete a User",
    button_query_user: "Query about a User",
    button_import_user: "Import a User",
    button_export_user: "Export a User",
    button_reset_password: "Reset Password",
    button_menu_add: "Add a Menu",
    button_menu_update: "Modify a Menu",
    button_menu_del: "Delete a Menu",
    button_menu_query: "Query about a Menu",
    button_dict_add: "Add a Dictionary",
    button_dict_update: "Modify a Dictionary",
    button_dict_del: "Delete a Dictionary",
    button_dict_query: "Query about a Dictionary",
    button_data_add: "Add Data",
    button_data_update: "Modify Data",
    button_data_del: "Delete Data",
    button_data_query: "Query about Data",
    button_config_add: "Add a Parameter",
    button_config_update: "Modify a Parameter",
    button_config_del: "Delete a Parameter",
    button_config_query: "Query about a Parameter",
    button_login_log_del: "Delete a login log",
    button_login_log_query: "Query about a login log",
    button_login_log_export: "Export a login log",
    button_operation_log_del: "Delete an operation log",
    button_operation_log_query: "Query about an operation log",
    button_operation_log_export: "Export an operation log",
    button_back_add: "Add a backup",
    button_back_reduction: "Restore a backup",
    button_back_del: "Delete a backup",
    button_back_query: "Query about a backup",
    button_group_add: "Add a Group",
    button_group_update: "Modify a Group",
    button_area_add: "Area addition",
    button_area_update: "Area modification",
    button_group_del: "Delete a Group",
    button_group_query: "Query about a Group",
    button_share_file: "Share a file",
    button_material_add: "Add Material",
    button_material_del: "Delete Material",
    button_material_query: "Query about material",
    button_material_model: "Model Deployment",
    button_material_examine: "Material View",
    button_job_add: "Add a Task",
    button_job_update: "Edit a Task",
    button_job_del: "Delete a Task",
    button_job_query: "Query about a Task",
    button_job_stop: "Stop a Task",
    button_job_start: "Start a Task",
    button_job_reset: "Reset a Task",
    button_job_log_query: "Query about a Task log",
    button_job_log_del: "Delete a Task log",
    button_shortcut_add: "Add a Shortcut",
    button_shortcut_update: "Modify a Shortcut",
    button_shortcut_del: "Delete a Shortcut",
    button_shortcut_query: "Query about a Shortcut",
    button_template_add: "Add a Template",
    button_template_update: "Edit a Template",
    button_template_del: "Delete a Template",
    button_template_query: "Query about a Template",
    button_program_add: "Add a Program",
    button_program_update: "Modify a Program",
    button_program_del: "Delete a Program",
    button_program_query: "Query a Program",
    button_program_export: "Export a Program",
    button_scheduling_add_common: "Add Digital Signage Schedule",
    button_scheduling_add_link: "Add Associate Signage Schedule",
    button_scheduling_update: "Modify a Schedule",
    button_scheduling_del: "Delete a Schedule",
    button_scheduling_query: "Query about a Schedule",
    button_scheduling_export: "Export play plan",
    button_device_add: "Add Screen",
    button_device_update: "Modify a Device",
    button_device_del: "Delete a Device",
    button_device_query: "Query about a Device",
    button_device_export: "Export a Device",
    button_version_add: "Add a Version",
    button_version_update: "Modify a Version",
    button_version_del: "Delete a Version",
    button_version_query: "Query about a Version",
    button_version_export: "Export a Version",
    button_download_resend: "Resend",
    button_download_progress: "Download in Progress",
    button_download_del: "Delete the Download",
    button_download_query: "Query about the Download",
    button_test_export: "Export a test",
    button_scheduling_audit: "Audit Scheduling",
    button_playRecord_del: "Delete a Play record",
    button_playRecord_query: "Query about a Play record",
    button_playRecord_export: "Export a Play record",
    button_appLog_del: "Delete a Device log",
    button_appLog_query: "Query about a Device log",
    button_appLog_export: "Export a Device log",
    button_caption_add: "Add a Caption",
    button_caption_update: "Modify a Caption",
    button_caption_del: "Delete a Caption",
    button_caption_query: "Query about a Caption",
    button_caption_audit: "Audit a Caption",
    button_caption_export: "Export a Caption",
    button_device_auth: "Device Authentication",
    button_device_clear: "Clear a program",
    button_device_audio_mute: "Mute",
    button_device_audio_set: "Volume Settings",
    button_device_reboot: "Reboot",
    button_device_timing: "Timing",
    button_device_start: "Start Playing",
    button_device_stop: "Stop Playing",
    button_device_upgrade: "Application Upgrade",
    button_device_mode_set: "Mode",
    button_device_backlight: "Backlight Switch",
    button_device_brightness: "Brightness Settings",
    button_device_rotate: "Rotation Settings",
    button_device_hav: "Horizontal and Vertical Screen Settings",
    button_device_screenshot: "Screenshot",
    button_close_accelerate: "Close Accelerate",
    button_open_accelerate: "Open Accelerate",

    device_passenger_flow_analyse: "Passenger flow analysis",
    device_passenger_flow_monitor: "Passenger flow monitoring",
    play_list: "Playlist",
    add_play_list: "Add Playlist",
    edit_play_list: "Edit Playlist",
    media_screen: "Signage Management",
    media_scrren_list: "Digital Signage",
    media_link_screen_list: "Associated Signage",
    media_report: "Report management",
    media_report_play: "Playback record",
    pull_screen_log: "Device Log Pull",
    screen_delete: "Delete Device",
    get_screen_store_id: "Get Device by Store",
    screen_log_list: "Device Log List",
    add_link_screen: "Add Link Screen",
    get_linkScreen: "Get Link Screen Details",
    edit_linkScreen: "Edit Link Screen",
    delete_linkScreen: "Delete Link Screen",
    link_screen_list: "Query Devices under Link Screen Group",
    get_linkScreen_status: "Get Link Screen Group Device Online Status",
    add_screen: "Add Digital Signage",
    quyery_screen: "Get Digital Signage Details",
    update_screen: "Update Screen Device",
    upgrade_screen: "OTA Upgrade Digital Signage",
    export_screen: "Export Digital Signage",
    export_subscribe_record: "Export Subscription Records",
    area_option_tree: "Area Dropdown Tree",
    area_save: "Add Area",
    area_edit: "Edit Area",
    area_delete: "Delete Area",
    area_get: "Get Area Details",
    store_option: "Store Dropdown",
    store_editor: "Edit Store",
    store_import: "Import Store",
    store_save: "Add Store",
    store_remove: "Remove Store",
    store_get: "Get Store Details",
    total_outlet_add: "Add Total Outlet",
    total_outlet_update: "Update Total Outlet",
    total_outlet_remove: "Remove Total Outlet",
    total_outlet_get: "Get Total Outlet Details",
    locationType_add: "Add Location Type",
    locationType_edit: "Edit Location Type",
    locationType_remove: "Remove Location Type",
    localtionType_option: "Location Type Dropdown",
    merchant_add: "Add Principal",
    merchant_edit: "Edit Principal",
    merchant_remove: "Remove Principal",
    playlist_add: "Add Playlist",
    playlist_remove: "Remove Playlist",
    playlist_get: "Get Playlist",
    playlist_edit: "Edit Playlist",
    schedule_linkScreen_push: "Publish Link Screen Schedule",
    linkScreen_schedule_save: "Add Link Screen Schedule",
    scheduling_remove: "Remove Schedule",
    screen_playlist_schedule_add: "Add Screen Playlist Schedule",
    screen_edit_playList: "Edit Screen Playlist Schedule",
    scheduling_link_update: "Update Link Screen Schedule",
    scheduling_push: "Publish Screen Playlist Schedule",
    user_status_enable: "Enable/Disable Account",
    user_resetPassword: "Reset Password",
    user_query: "Get User Details",
    user_delete: "Delete User",
    user_update: "Update User",
    create_user: "Create User",
    role_add: "Add Role",
    role_query: "Get Role Details",
    add_application: "Add Application",
    del_application: "Delete Application",
    update_application: "Update Application",
    menu_add: "Add Menu",
    menu_edit: "Edit Menu",
    menu_remove: "Remove Menu",
    menu_query: "Get Menu Details",
    screenShot_delete: "Delete Device Screenshot",
    operLog_clear: "Clear Operation Log",
    logLog_clear: "Clear Login Log",
    upload_material: "Upload Material",
    save_layout: "Save Layout Resource",
    edit_layout: "Edit Layout Resource",
    resource_audit: "Resource Audit",
    material_delete: "Delete Material",
    area_accelerate_edit: "Regional acceleration",
    material_group: "Material grouping",
    material_group_add: "New material group",
    material_group_update: "Modify material group",
    material_group_delete: "Delete a material group",
    screen_sync_timezone: "Synchronize store time zones",
    device_reboot: "Device reboot"
  },
  //continent 大洲
  continent: {
    common_continent_Asia: "Asia",
    common_continent_Europe: "Europe",
    common_continent_Afrika: "Africa",
    common_continent_Oceania: "Oceania",
    common_continent_NorthAmerica: "North America",
    common_continent_SouthAmerica: "South America",
    common_continent_CentralAmerica: "Central America",
    common_continent_Caribbean: "Caribbean",
  },
  //country 城市
  country: {
    common_country_DZ: "Algeria",
    common_country_EG: "Egypt",
    common_country_ET: "Ethiopia",
    common_country_AO: "Angola",
    common_country_BJ: "Benin",
    common_country_BW: "Botswana",
    common_country_BF: "Burkina Faso",
    common_country_BI: "Burundi",
    common_country_GQ: "Equatorial Guinea",
    common_country_TG: "Togo",
    common_country_ER: "Eritrea",
    common_country_CV: "Cape Verde",
    common_country_GM: "Gambia",
    common_country_CD: "Congo",
    common_country_CG: "Democratic Republic of the Congo",
    common_country_KG: "Djibouti",
    common_country_GN: "Guinea",
    common_country_GW: "Guinea-Bissau",
    common_country_GH: "Ghana",
    common_country_GA: "Gabon",
    common_country_ZW: "Zimbabwe",
    common_country_CM: "Cameroon",
    common_country_KM: "Comoros",
    common_country_CI: "Cote d Ivoire",
    common_country_KE: "Kenya",
    common_country_LS: "Lesotho",
    common_country_LR: "Liberia",
    common_country_LY: "Libyan Arab Jamahiriya",
    common_country_RE: "Réunion",
    common_country_RW: "Rwanda",
    common_country_MG: "Madagascar",
    common_country_MW: "Malawi",
    common_country_ML: "Mali",
    common_country_YT: "Mayotte",
    common_country_MU: "Mauritius",
    common_country_MR: "Mauritania",
    common_country_MA: "Morocco",
    common_country_MZ: "Mozambique",
    common_country_NA: "Namibia",
    common_country_ZA: "South Africa",
    common_country_NE: "Niger",
    common_country_NG: "Nigeria",
    common_country_SL: "Sierra Leone",
    common_country_SN: "Senegal",
    common_country_ST: "Sao Tome & Principe",
    common_country_SZ: "Swaziland",
    common_country_SD: "Sudan",
    common_country_SO: "Somalia",
    common_country_TZ: "Tanzania",
    common_country_TN: "Tunisia",
    common_country_UG: "Uganda",
    common_country_EH: "Western Sahara",
    common_country_ZM: "Zambia",
    common_country_TD: "Chad",
    common_country_CF: "Central African Republic",
    common_country_AF: "Afghanistan",
    common_country_AE: "United Arab Emirates",
    common_country_OM: "Oman",
    common_country_AZ: "Azerbaijan",
    common_country_MO: "Macao,China",
    common_country_PK: "Pakistan",
    common_country_PS: "Palestine",
    common_country_BH: "Bahrain",
    common_country_BT: "Bhutan",
    common_country_KP: "North Korea",
    common_country_TL: "Timor-Leste",
    common_country_PH: "Philippines",
    common_country_GE: "Georgia",
    common_country_KZ: "Kazakhstan",
    common_country_KR: "South Korea",
    common_country_DJ: "Kyrgyzstan",
    common_country_KH: "Cambodia",
    common_country_QA: "Qatar",
    common_country_KW: "Kuwait",
    common_country_LA: "Lao",
    common_country_LB: "Lebanon",
    common_country_MV: "Maldives",
    common_country_MY: "Malaysia",
    common_country_MN: "Mongolia",
    common_country_BD: "Bangladesh",
    common_country_MM: "Myanmar",
    common_country_NP: "Nepal",
    common_country_JP: "Japan",
    common_country_CY: "Cyprus",
    common_country_SA: "Saudi Arabia",
    common_country_LK: "Sri Lanka",
    common_country_TJ: "Tajikistan",
    common_country_TH: "Thailand",
    common_country_TR: "Turkey",
    common_country_TM: "Turkmenistan",
    common_country_BN: "Brunei Darussalam",
    common_country_UZ: "Uzbekistan",
    common_country_HK: "Hongkong,China",
    common_country_SG: "Singapore",
    common_country_SY: "Syrian Arab Republic",
    common_country_AM: "Armenia",
    common_country_YE: "Yemen",
    common_country_IQ: "Iraq",
    common_country_IR: "Iran",
    common_country_IL: "Israel",
    common_country_IN: "India",
    common_country_ID: "Indonesia",
    common_country_JO: "Jordan",
    common_country_VN: "Viet Nam",
    common_country_CN: "Mainland China",
    common_country_TW: "Taiwan",
    common_country_AW: "Aruba",
    common_country_BB: "Barbados",
    common_country_BS: "Bahamas",
    common_country_PR: "Puerto Rico",
    common_country_DO: "Dominican Republic",
    common_country_GD: "Grenada",
    common_country_CU: "Cuba",
    common_country_GP: "Guadeloupe",
    common_country_HT: "Haiti",
    common_country_AN: "Netherlands Antilles",
    common_country_MQ: "Martinique",
    common_country_LC: "Saint Lucia",
    common_country_VC: "Saint Vincent",
    common_country_TT: "Trinidad & Tobago",
    common_country_JM: "Jamaica",
    common_country_VG: "Virgin Islands",
    common_country_PA: "Panama",
    common_country_BZ: "Belize",
    common_country_CR: "Costa Rica",
    common_country_HN: "Honduras",
    common_country_NI: "Nicaragua",
    common_country_SV: "El Salvador",
    common_country_GT: "Guatemala",
    common_country_AL: "Albania",
    common_country_IE: "Ireland",
    common_country_EE: "Estonia",
    common_country_AT: "Austria",
    common_country_BY: "Belarus",
    common_country_BG: "Bulgaria",
    common_country_BE: "Belgium",
    common_country_IS: "Iceland",
    common_country_PL: "Poland",
    common_country_BA: "Bosnia & Herzegovina",
    common_country_DK: "Denmark",
    common_country_DE: "Germany",
    common_country_RU: "Russia",
    common_country_FR: "France",
    common_country_FI: "Finland",
    common_country_NL: "Netherlands",
    common_country_ME: "Montenegro",
    common_country_CZ: "Czech Republic",
    common_country_HR: "Croatia",
    common_country_LV: "Latvia",
    common_country_LT: "Lithuania",
    common_country_LU: "Luxembourg",
    common_country_RO: "Romania",
    common_country_MT: "Malta",
    common_country_MK: "Macedonia",
    common_country_MD: "Moldova",
    common_country_NO: "Norway",
    common_country_PT: "Portugal",
    common_country_SE: "Sweden",
    common_country_CH: "Switzerland",
    common_country_RS: "Serbia",
    common_country_SK: "Slovakia",
    common_country_SI: "Slovenia",
    common_country_UA: "Ukraine",
    common_country_ES: "Spain",
    common_country_GR: "Greece",
    common_country_HU: "Hungary",
    common_country_IT: "Italy",
    common_country_UK: "England",
    common_country_AD: "Andorra",
    common_country_VA: "Vatican",
    common_country_CA: "Canada",
    common_country_US: "USA",
    common_country_MX: "Mexico",
    common_country_AU: "Australia",
    common_country_PG: "Papua New Guinea",
    common_country_PF: "French Polynesia",
    common_country_FJ: "Fiji",
    common_country_GU: "Guam",
    common_country_KI: "Kiribati‎",
    common_country_CK: "Cook Islands",
    common_country_MH: "Marshall Islands‎",
    common_country_FM: "Micronesia",
    common_country_NR: "Nauru",
    common_country_WS: "Samoa",
    common_country_SB: "Solomon Islands",
    common_country_TO: "Tonga",
    common_country_TV: "Tuvalu",
    common_country_VU: "Vanuatu",
    common_country_NC: "New Caledonia",
    common_country_NZ: "New Zealand",
    common_country_AR: "Argentina",
    common_country_PY: "Paraguay",
    common_country_BR: "Brazil",
    common_country_BO: "Bolivia",
    common_country_EC: "Ecuador",
    common_country_GF: "French Guiana",
    common_country_CO: "Colombia",
    common_country_GY: "Guyana",
    common_country_PE: "Peru",
    common_country_SR: "Suriname",
    common_country_VE: "Venezuela",
    common_country_UY: "Uruguay",
    common_country_CL: "Chile",
  },
  //license相关
  license: {
    auth_license_reason: "Reasons:",
    auth_license_steps: "You can try to fix it by following below steps:",
    auth_license_errtxt_1:
      "The information used to activate the license is incorrect or incomplete.",
    auth_license_errreason_1:
      "1.The information filled in the online or offline activation page is incorrect, incomplete or can't be decoded.",
    auth_license_errsteps_1:
      "1.Fill in the activation information and try again.<br/>2.If the previous method doesn't solve the problem, please contact our technical support or sales staff.",
    auth_license_errtxt_2:
      "The email has been used to register other licenses.",
    auth_license_errreason_2:
      "The email has been used to register the license: {0}.",
    auth_license_errsteps_2:
      "1.Try using a new email address.<br/>2.If the previous method doesn't solve the problem, please contact our technical support or sales staff.",
    auth_license_errtxt_3: "The license has been activated previously.",
    auth_license_errreason_3:
      "1.The SN file has been activated on another computer.<br/>2.The hardware information in the SN file does not match the current computer or the hardware information of the current computer has changed recently.",
    auth_license_errsteps_3:
      "1.Confirm whether the SN file has been used before and was not used in the current computer.<br/>2.Confirm whether the hardware changed recently.<br/>3.Get the SN file and try again.<br/>4.If your computer is a virtual machine, please inform us promptly.<br/>5.If the previous methods don't solve the problem, please contact our technical support or sales staff.",
    auth_license_errtxt_4: "Data was lost during online activation.",
    auth_license_errreason_4:
      "During the online activation process, the data was tampered, resulting invalid parameters.",
    auth_license_errsteps_4:
      "1.Click the [Activate] button and try again.<br/>2.Try the [Offline Activation].<br/>3.If the previous methods don't solve the problem, please contact our technical support or sales staff.",
    auth_license_errtxt_E0005:
      "The software version you have installed is not recommended.",
    auth_license_errreason_E0005:
      "The software version you have installed is obsolete or may meet some problems, so its no longer recommended.",
    auth_license_errsteps_E0005:
      "1.Visit to the official website to download the latest update package to upgrade the current installed version or download the latest installation package, uninstall the current version and install the new version.<br/>2.If the previous methods don't solve the problem, please contact our technical support or sales staff.",
    auth_license_errtxt_E0006:
      "Unable to connect to the license server because of local network problems.",
    auth_license_errreason_E0006:
      "1.The computer is not connected to the network.<br/>2.There are problems in the network environment. <br/>3.A firewall is blocking the connection.<br/>4.Check whether you are using a proxy server.",
    auth_license_errsteps_E0006:
      "1.Use the network diagnostic tool in the system for network diagnosis.<br/>2.Check whether a firewall is blocking the connection.<br/>3.Don't use a proxy server, this may create conflicts.<br/>4.If the previous methods don't solve the problem, please contact our technical support or sales staff.",
    auth_license_errtxt_E0007: "An error occurred while importing the SN file.",
    auth_license_errreason_E0007:
      "1.The SN file was tampered.<br/>2.The SN file data was lost during the process.<br/>3.The uploaded file is not a SN file, please confirm.",
    auth_license_errsteps_E0007:
      "1.Restore modifications made to the SN file.<br/>2.Confirm you are using an original SN file.<br/>3.Try the offline activation method.<br/>4.If the previous methods don't solve the problem, please contact our technical support or sales staff.",
    auth_license_errtxt_E0008:
      "An error occurred during the license file import process.",
    auth_license_errreason_E0008:
      "1.The license file was tampered.<br/>2.The license file data was lost during the process.<br/>3  The uploaded file is not a license file.",
    auth_license_errsteps_E0008:
      "1.Restore modifications made to the license file.<br/>2.Confirm you are using a valid license file.<br/>3.Try the offline activation method.<br/>4.If the previous methods don't solve the problem, please contact our technical support or sales staff.",
    auth_license_errtxt_E0009: "The request to the license server timeout.",
    auth_license_errreason_E0009:
      "1.Network bandwith is low.<br/>2.Network connection was interrupted.",
    auth_license_errsteps_E0009:
      "1.Check the network enviroment or change to another network and try again.<br/>2.If the previous methods don't solve the problem, please contact our technical support or sales staff.",
    auth_license_errtxt_E0010:
      "The license you imported is the activation {1} of {0} which does not apply to the software you are currently using.",
    auth_license_errreason_E0010:
      "The license you imported is the activation {1} of {0} which does not apply to the software you are currently using.",
    auth_license_errsteps_E0010:
      "You are currently using {0}, please reapply for a {0} license.",
    auth_license_errtxt_E0011: "This license is invalid.",
    auth_license_errreason_E0011:
      "1.The license has been set as invalid in the license server.<br/>2.The license has been deleted by the administrator because the server was changed.<br/>3.The language of installation package is not matched with the license.",
    auth_license_errsteps_E0011:
      "1.Reapply for the correct license file and try again.<br/>2.If the previous method doesn't solve the problem, please contact our technical support or sales staff.",
    auth_license_info: "License Information",
    auth_license_id: "License ID",
    auth_user_companyName: "Authorized Company: ",
    auth_license_item: "Item",
    auth_license_status: "Status",
    auth_license_remain: "Available points/Total points",
    auth_license_validdays: "Expiration Date",
    auth_license_activate: "Activation",
    auth_license_period_nonActivated: "Non activated",
    auth_license_activateOnline: "Online Activation",
    auth_license_activateOffline: "Offline Activation",
    auth_license_continents: "Continents",
    auth_license_country: "Country/Region",
    auth_license_city: "City",
    auth_license_companyName: "Company Name",
    auth_license_industry: "Industry",
    auth_license_persons: "Personnel",
    auth_license_contacts: "Contacts",
    auth_license_mobile: "Mobile",
    auth_license_countryCode: "International Area Code",
    auth_license_phoneAreaCode: "Area Code",
    auth_license_phone: "Phone",
    auth_license_Extension: "Extension",
    auth_user_email: "Email",
    auth_license_address: "Address",
    auth_license_productsSuppliers: "Dealer's Name",
    auth_license_snFile: "The Serial Number File",
    auth_license_period_activated: "Activated",
    auth_license_have_file: "Have a license or not",
    auth_license_download_upk: "Download upk file",
    auth_license_upload_file: "Upload file",
    auth_license_confirmOff:
      "Do you have an existing license file whose format is “*-license*.xml“?",
    auth_license_offActivationCodeDownload: "Offline activation file download.",
    auth_license_offDownloadPrompt:
      "The file needs to be sent to the relevant suppliers to generate returns activation code files!",
    auth_license_file: "License file",
  },
  message: {
    messageBox_title: "Prompt",
    industryStr:
      "Financial||Construction||Manufacturing||Education||Multi Office Buidings||Principal Stores||Utility/Communication||Health Care Facilities||State&Local Government",
    //模块
    module_77: "Information Publish",
  },
  system: {
    //系统配置
    base_system_account: "Account",
    base_system_password: "Password",
    base_system_verification_code: "Verification Code",
    base_system_about: "About",
    base_system_login: "Log in",
    base_system_logging_in: "Logging in",
    base_system_remember: "Remember the Password",
    base_system_browsers: "Browser is recommended for this system",
    base_system_resolution: "Display Resolution",
    base_system_pixels: "Pixels and Above",
    base_system_environment: "Software Running Environment",
    system_rule_user: "The User Name cannot be empty",
    system_rule_password: "Password cannot be empty",
    system_rule_code: "Verification Code cannot be empty",
    system_rule_company_name: "Company Name cannot be empty",
    system_rule_email: "Please enter the correct email address",
    system_rule_phoneCountryCode: "Area Code cannot be empty",
    system_rule_mobile: "Please enter the correct mobile Phone Number",
    system_rule_phone: "Phone Number cannot be empty",
    system_rule_city: "City cannot be empty",
    system_rule_industry: "Industry selection cannot be blank",
    system_rule_persons: "The number of people cannot be empty",
    system_rule_contacts: "Contact cannot be empty",
    system_rule_address: "Address cannot be empty",
    system_rule_products_suppliers: "Product Suppliers cannot be empty",
    system_rule_user_name: "User Name cannot be empty",
    system_rule_user_name_exist: "The User already exists",
    system_rule_user_name_max:
      "The Length of user name should not exceed 30 digits",
    system_rule_user_nickname: "User nickname cannot be empty",
    system_rule_user_nickname_max:
      "The Length of user nickname should not exceed 30 digits",
    system_rule_user_dept:
      "The name of the affiliated organization cannot be empty",
    system_rule_user_password: "User Password cannot be empty",
    system_rule_user_email: "Email Address cannot be empty",
    system_rule_user_email_format: "Please enter the correct email address",
    system_rule_user_mobile: "Mobile Phone Number cannot be empty",
    system_msg_snFile: "SN file cannot be empty",
    system_msg_licFile: "The license file cannot be empty",
    system_msg_snFile_err: "Failed to upload SN file",
    system_msg_activateOnline_success: "Online Activation Successful",
    system_msg_activateOnline_fail: "Online Activation Failed",
    system_msg_activateOffline_success: "Offline Activation Successful",
    system_msg_activateOffline_fail: "Offline Activation Failed",
    system_only_xml: "Only XML files can be uploaded",
    system_material_statistics: " Statistics Material ",
    system_material_upload: " Resource Upload",
    system_image_upload: "Image Upload",
    system_material_total: "Total Material",
    system_update_user_data: "Update Existing User Data",
    system_download_template: " Download Template ",
    system_import_tip:
      'Tip: It only supports "XLS" or "xlsx" Format Files can be Imported!',
    system_confirm: 'Are you sure you want to use{state}"{userName}"user',
    system_reset_password: 'Please enter a New Password for"{userName}"',
    system_msg_new_password: "The new password is:{password}",
    system_del_user: `Are you sure you want to delete the data entry for user with name "{{userNo}}"?`,
    system_export_user:
      "Are you sure whether you want to export all the user data items?",
    system_user_import_title: " Import User ",
    system_import_result: "Import Result",
    system_upload_avatar: "Upload Avatar",
    system_update_avatar: "Change Avatar",
    system_msg_file_fail:
      "File format error, please upload image type, such as: JPG, PNG suffix file.",
    system_msg_input_password_fail: "Passwords are not matching",
    system_rule_old_password_null: "Old Password cannot be empty",
    system_rule_new_password_null: "New Password cannot be empty",
    system_rule_confirm_password_null: "Confirm Password cannot be empty",
    system_rule_password_length:
      "The Length of Password should be in between 6 to 30 characters",
    system_user_info: "Personal Information",
    system_base_info: "Basic Information",
    system_rule_name_length: "The Length of Name should not exceed 30 digits",
    system_add_shortcut: "Add shortcut",
    system_update_shortcut: "Modify Shortcut",
    system_del_shortcut:
      'Are you sure you want to delete the data item with shortcut number"{ids}"?',
    system_data_rights_option_1: "All Data Permissions",
    system_data_rights_option_2: "Custom Data Permissions",
    system_data_rights_option_3: "Data authority of the organization",
    system_data_rights_option_4: "Data authority of the organization and below",
    system_data_rights_option_5: "Data Permission Only",
    system_rule_role_name_length:
      "The Length of role name cannot exceed 60 digits",
    system_rule_role_order_null: "Role order cannot be empty",
    system_role_update_status:
      'Are you sure you want to play the{op}"{name}"role?',
    system_add_role: "Add Role",
    system_update_role: "Update Role",
    system_distribution_rights: "Assign Data Rights",
    system_delete_role:
      'Are you sure you want to delete the data item with the role number"{roleId}"?',
    system_menu_name: "Menu Name",
    system_input_menu_name: "Please enter the name of the menu",
    system_menu_state: "Menu Status",
    system_rights_flag: "Authority Identification",
    system_input_rights_flag: "Please enter Permission ID",
    system_assembly_path: "Component Path",
    system_input_assembly_path: "Please enter the Component Path",
    system_parent_menu: "Previous Menu",
    system_menu_non_existent: "The menu does not exist",
    system_select_parent_menu: "Select Superior Menu",
    system_menu_type: "Menu Type",
    system_menu: "Menu",
    system_catalogue: "Contents",
    system_resources: "Resources",
    system_menu_icon: "Menu Icon",
    system_route_address: "Router Address",
    system_input_route_address: "Please enter the Router Address",
    system_display_order: "Display Sort",
    system_cache: "Cache",
    system_rule_menu_name: "Menu Name cannot be empty",
    system_rule_menu_name_length:
      "The length of menu name should not exceed 30 digits",
    system_rule_menu_order: "Menu order cannot be empty",
    system_rule_menu_route: "Router Address cannot be empty",
    system_rule_menu_route_length:
      "Router address length cannot exceed 128 bits",
    system_main_category: "Main categories",
    system_add_menu: "Add menu",
    system_update_menu: "Modify menu",
    system_del_menu:
      'Are you sure you want to delete the data item with the name"{name}"?',
    system_file_name: "File name",
    system_input_file_name: "Please enter a file name",
    system_file_type: "File category",
    system_input_file_type: "Please enter file category",
    system_file_no: "File Number",
    system_thumbnail: "Thumbnail",
    system_file_category: "File Type",
    system_file_format: "File Format",
    system_file_size: "File Size",
    system_upload_file_size:
      "Tip: It supports up to 5 files to upload at a time!",
    system_upload_file: " Upload File ",
    system_upload_result: "Upload Results",
    system_file_share: "File Sharing",
    system_del_file:
      'Are you sure you want to delete the data item with file number"{fileId}"?',
    system_dict_name: "Dictionary Name",
    system_dict_label: "Dictionary Label",
    system_input_dict_label: "Please enter the dictionary label",
    system_dict_no: "Dictionary Number",
    system_dict_value: "Dictionary Key Value",
    system_dict_type: "Dictionary Type",
    system_input_data_label: "Please enter the data label",
    system_input_data_value: "Please enter the data key value",
    system_rule_data_label: "Data Label cannot be empty",
    system_rule_data_value: "Data Key Value cannot be empty",
    system_rule_data_order: "Data Order cannot be empty",
    system_add_dict_data: "Add Dictionary Data",
    system_update_dict_data: "Update Dictionary Data",
    system_del_dict_data:
      "Confirm to delete the data item with dictionary code '{dictNo}'?",
    system_input_dict_type: "Please input dictionary type",
    system_dict_state: "Dictionary State",
    system_built_in: "Built In System",
    system_rule_dict_name: "Dictionary Name cannot be empty",
    system_rule_dict_type: "Dictionary Type cannot be empty",
    system_add_dict_type: "Add Dictionary Type",
    system_update_dict_type: "Update Dictionary Type",
    system_del_dict:
      "Confirm whether to delete the data item with dictionary number '{dictNo}'?",
    system_export_dict: "Confirm whether to export all types of data items?",
    system_dept_state: "Department State",
    system_dept_code: "Department Code",
    system_parent_dept: "Parent Department",
    system_select_parent_dept: "Select Parent Department",
    system_input_dept_code: "Please input department code",
    system_concat_mobile: "Mobile Number",
    system_input_concat_mobile: "Please input",
    system_rule_parent_dept: "Parent Department name cannot be empty",
    system_rule_dept_name: "Department name cannot be empty",
    system_rule_dept_name_length:
      "The length of department name cannot exceed 60 digits",
    system_rule_dept_code: "Department Code cannot be empty",
    system_rule_dept_code_length:
      "The length of department code cannot exceed 30 digits",
    system_add_dept: "Add Department",
    system_update_dept: "Update Department",
    system_del_dept:
      "Confirm whether to delete the data item with the name '{name}'?",
    system_back_name: "Backup Name",
    system_input_back_name: "Please enter the backup name",
    system_reduction: "Reduction",
    system_back_no: "Back Number",
    system_back_path: "Back Path",
    system_explain: "Explain",
    system_back_tip:
      "Please confirm that the Postgresql installation path has been configured into the environmental variable path",
    system_rule_remakes: "Remarks cannot be empty",
    system_confirm_reduction:
      "Confirm whether to restore the data item with backup number '{no}'?",
    system_msg_reduction_success: "Reduction Successful",
    system_reduction_success: " Reduction Successful",
    system_msg_back: "Backing up, please wait...",
    system_msg_reduction: "Restoring, please wait...",
    system_confirm_del_back:
      "Confirm whether to delete the data item with backup number '{no}?'",
    system_group_type: "Group Type",
    system_group_no: "Group Number",
    system_parent_no: "Parent Number",
    system_select_parent_no: "Please select parent number",
    system_rule_group_name_length:
      "The length of group name cannot exceed 60 digits",
    system_rule_parent_no: "Parent number cannot be empty",
    system_rule_group_type: "Group type cannot be empty",
    system_rule_group_type_length:
      "The length of group type cannot exceed 10 bits",
    system_top_level: "Top Level",
    system_add_group: "Add Group",
    system_update_group: "Update Group",
    system_del_group:
      "Confirm whether to delete the data item with group number '{no}'?",
    system_login_user: "Login User",
    system_input_login_user: "Please input login user",
    system_login_address: "Login Address",
    system_input_login_address: "Please input login address",
    system_login_state: "Login State",
    system_clean: "Clean",
    system_login_time: "Login Time",
    system_visit_no: "Visit Number",
    system_type: "Type",
    system_login_place: "Login Place",
    system_user_agent: "User Agent",
    system_exception: "Exception",
    system_login_date: "Login Date",
    system_login_data_clear: "Confirm whether to clear all login log data?",
    system_del_login_data:
      "Confirm whether to delete the data item with access number '{no}'?",
    system_msg_clear_success: "Clear Success",
    system_login_data_export:
      "Confirm whether to export all operation log data items?",
    system_title: "Title",
    system_input_title: "Please input the Title",
    system_operator: "Operator",
    system_input_operator: "Please input the Operator",
    system_operation_state: "Operation State",
    system_operation_time: "Operation Time",
    system_log_no: " No Log.",
    system_request_method: "Request Method",
    system_ip: "IP Address",
    system_operation_place: "Operation Place",
    system_client: "Client",
    system_request_time: "Request Time",
    system_operation_date: "Operation Date",
    system_operation_log_detail: "Detailed Operation Log(s)",
    system_operation_module: "Operation Module",
    system_login_info: "Login Info",
    system_request_address: "Request Address",
    system_request_params: "Request Parameters",
    system_fail: "Fail",
    system_del_operation_log:
      "Confirm whether to delete the data item with log number '{no}'?",
    system_clear_operation_log:
      "Confirm whether to clear all operation log data items？",
    system_export_operation_log:
      "Confirm whether to export all operation log data items?",
    system_custom_shortcut: "Custom Shortcut",
    system_non_add_custom_shortcut: "You haven't added any shortcut yet!",
    system_click_add_custom_shortcut: "Click here to add",
    system_add_backup: "Add backup",
    system_add_user: "Add User",
    system_edit_user: "Edit User",
    system_user_primary_id: "Primary Key",
    system_user_id: "User Id",
    system_user_surname: "Surname",
    system_user_name: "User Name",
    system_user_email: "User Email",
    system_user_phone: "User Phone",
    system_belong_merchant: "Principal",
    system_user_position: "Position",
    system_user_devops: "Operator",
    system_user_retailer: "Principal",
    system_user_retailerClient: "Employee of Principal",
    system_user_enployee_advertiser: "Employee of Advertiser",
    system_user_advertiser: "Principal Client",
    // 8/29
    system_basic: "Core",
    system_role_devopt: "Operations Personnel",
    system_role_brand: "Brand Side",
    system_role_advertiser: "Advertisers",
    system_role_super_admin: "Super Admin",
    system_security_setting: "Security Settings",
    system_you_login_system: "Logged in at {{accessTime}}",
    system_you_oper_server: "You performed {{method}} at {{operationTime}}.",
    system_location_os: "Location: {{location}}, OS: {{os}}",
    system_login_current_ip: "IP Address: {{ip}}",
    system_login_success: "Successful login",
    system_login_error: "Failed login",

    system_user_package: "package",
    system_third_application_name: "Application Name",
    system_third_application_id: "Application ID",
    system_third_application_secret: "Application Secret",
    system_enter_third_application_name: "Please enter the application name",
    system_add_application: "Add Application",
    system_edit_application: "Edit Application",
  },
  server: {
    server_tcpPort: "Server Port：",
    server_downPort: "Download Port File：",
    server_timing_issue: "Issue Scheduled ",
    server_terminal_offline: "The terminal is offline",
    server_scheduling_add: "Add Digital Signage Schedule",
    server_scheduling_link_add: "Add Video Wall Schedule",
    server_scheduling_update: "Edit Digital Signage Schedule",
    server_scheduling_link_upload: "Edit Associated signage schedule",
    server_scheduling_push: "Publish Digital Signage Schedule",
    server_scheduling_link_push: "Publish Video Wall Schedule",
    server_scheduling_del: "Remove Schedule",
    server_scheduling_export: "Scheduled export",
    server_application_log_add: "Add Application Log",
    server_application_log_update: "Update Application Log",
    server_application_log_del: "Delete Application Log",
    server_plugin_in_add: "Add Plugin",
    server_plugin_in_update: "Update Plugin",
    server_plugin_in_del: "Delete Plugin",
    server_instruct_update: "Update Instruction",
    server_instruct_del: "Delete Instruction",
    server_device_add: "Add Device",
    server_device_update: "Update Device",
    server_device_del: "Delete Device",
    server_program_add: "Add Program",
    server_program_update: "Update Program",
    server_program_del: "Delete Program",
    server_device_offline: "The device is offline",
    server_group_add: "Add Group",
    server_group_update: "Update Group",
    server_group_del: "Delete Group",
    server_heartbeat_add: "Add Heartbeat",
    server_heartbeat_update: "Update Heartbeat",
    server_heartbeat_del: "Delete Heartbeat",
    server_material_add: "Add Material",
    server_material_upload: "Upload Resource",
    server_material_update: "Update Material",
    server_material_del: "Delete Resource",
    server_group_move: "Move Group",
    server_playRecord_add: "Add Playback Record ",
    server_playRecord_update: "Update Playback Record",
    server_playRecord_del: "Delete Playback Record",
    server_item_add: "Add Program",
    server_item_update: "Update Program",
    server_item_del: "Delete Program",
    server_item_upload: "Upload Program",
    server_resDownload_add: "Add Download Progress",
    server_resDownload_update: "Update Download Progress",
    server_resDownload_del: "Delete Download Progress",
    server_resource_add: "Add Resource",
    server_resource_update: "Update Resource",
    server_resource_del: "Delete Resource",
    server_version_add: "Add Version",
    server_version_update: "Update Version",
    server_version_del: "Delete Version",
    server_job_add: "Add Timed Task",
    server_job_update: "Update Timed Task",
    server_job_del: "Delete Timed Task",
    server_job_run: "Run Timed Task",
    server_job_start_all: "Start all Timed Tasks",
    server_job_stop_all: "Stop all Timed Tasks",
    server_job_refresh: "Refresh all Timed Tasks",
    server_job_start: "Start Timed Task",
    server_job_stop: "Stop Timed Task",
    server_job_log_del: "Delete Timed Task log",
    server_application_add: "Add Application",
    server_application_update: "Update Application",
    server_application_del: "Delete Application",
    server_backup_add: "Add Backup",
    server_backup_del: "Delete Backup",
    server_backup_restore: "Restore Backup",
    server_client_add: "Add Client",
    server_client_update: "Update Client",
    server_client_del: "Delete Client",
    server_dept_add: "Add Department",
    server_dept_update: "Update Department",
    server_dept_del: "Delete Department",
    server_dept_state: "Update Departmental Status",
    server_loginLog: "Login",
    server_loginLog_del: "Delete Login",
    server_loginLog_clear: "Clear Login",
    server_menu_add: "Add Menu",
    server_menu_update: "Update Menu",
    server_menu_del: "Delete Menu",
    server_menu_state: "Update Menu Status",
    server_operLog: "Operational Log",
    server_operLog_del: "Delete Operational Log",
    server_operLog_clear: "Clear Operational Log",
    server_role_add: "Add Role",
    server_role_update: "Update Role",
    server_role_state: "Modify Role Status",
    server_role_data: "Data Permissions",
    server_role_del: "Delete Role",
    server_shortcut_add: "Add Shortcut",
    server_shortcut_update: "Update Shortcut",
    server_shortcut_del: "Delete Shortcut",
    server_user_add: "Add User",
    server_user_update: "Update User",
    server_user_del: "Delete User",
    server_user_info_update: "Update User Information",
    server_user_head_update: "Update User Profile",
    server_user_pwd_update: "Update User Password",
    server_user_pwd_reset: "Reset User Password",
    server_user_state: "Modify User Status",
    server_user_export: "Export User Data",
    server_user_import: "Import User Data",
    server_area_add: "Add Area",
    server_area_edit: "Edit Area",
    server_area_remove: "Remove Area",
    server_material_cover_upadte: "Update Content Resource Cover Image",
    server_merchant_add: "Add  Principal",
    server_merchant_edit: "Edit Mer Principalhant",
    server_merchant_remove: "Remove  Principal",
    server_playlist_add: "Add Playlist",
    server_playlist_edit: "Edit Playlist",
    server_playlist_remove: "Remove Playlist",
    server_add_screen: "Add Digital Signage",
    server_edit_screen: "Edit Digital Signage",
    server_remove_screen: "Remove Digital Signage",
    server_reboot_screen: "Reboot Digital Signage",
    server_shot_screen: "Screenshot Digital Signage",
    server_add_link_screen: "Add Associated Signage",
    server_edit_link_screen: "Edit Associated Signage",
    server_remove_link_screen: "Remove Associated Signage",
    server_edit_store: "Edit Outlet",
    server_add_store: "Add Outlet",
    server_remove_store: "Remove Outlet",
    server_init_register_company:
      "Initialize System Registered Company Account",
    server_center_info_edit: "Edit Personal Information",
    server_center_info_avatar_edit: "Edit Avatar",
    server_edit_user_info: "Edit User Information",
    server_rest_password_user: "Reset User Password",
    total_outlet_update: "Modify the total number of stores",
    total_outlet_add: "Total number of new stores",
    total_outlet_delete: "Delete total number of stores",
    upgrade_screen: "OTA upgrades digital signage",
    onclickpublish: "One-click publishing",
    server_location_remove: "Remove Store Location Type",
    server_location_edit: "Edit Store Location Type",
    server_location_add: "Add Store Location Type",
    server_screenshot_remove: "Remove Device Screenshot",
    server_tenant_edit: "Edit Tenant",
    server_tenant_add: "Add Tenant",
    server_tenant_remove: "Remove Tenant",
    server_copy_schedule: "Copy Schedule",
    server_one_click_schedule: "One-click Publish",
    server_material_group_delete: "Delete material group",
    server_material_group_update: "Update material group",
    server_material_group_add: "Add material group",
    server_ai_text_to_image: "AI Text-to-Image"
  },
  //编辑器国际化
  editor: {
    editor_componentList: "Component List",
    editor_programInfo: "Program Scenes({num})",
    editor_scene: "Scene",
    editor_del: "Delete",
    editor_copy: "Copy",
    editor_edit: "Edit",
    editor_add: "Add",
    editor_template: "Split Screen",
    editor_preview: "Preview",
    editor_save: "Save",
    editor_preserving: "Saving, please wait...",
    editor_cancel: "Return",
    editor_backOut: "Cancel",
    editor_recover: "Recover",
    editor_fit_screen: "Adapt to the screen",
    editor_full_size: "Actual Size",
    //样式
    editor_scene_style: "Scene Style",
    editor_bgColor: "Bg-Color",
    editor_bgImage: "Bg-Image",
    editor_script: "Custom Script",
    editor_attribute: "Attribute",
    editor_switch_effect: "Switching Effect",
    editor_play_properties: "Playback Attributes",
    editor_leisure: "Free",
    editor_busy: "Busy",
    editor_duration: "Duration",
    editor_style: "Style",
    editor_pictureName: "Layer Name",
    editor_textArea: "Text Content",
    editor_url: "Website Link",
    editor_show: "Terminal display",
    editor_select_audio: "Select Audio",
    editor_select: "Select",
    editor_province: "Province",
    editor_city: "City",
    editor_county: "County",
    editor_fontColor: "Font Color",
    editor_dataSources: "Data Sources",
    editor_scrollDirection: "Scroll Direction",
    editor_scrollSpeed: "Scroll Speed",
    editor_font: "Font",
    editor_fontSize: "Font Size",
    editor_bold: "Bold",
    editor_italy: "Tilt",
    editor_underLine: "Underscore",
    editor_contentLeft: "Align Left",
    editor_contentCenter: "Align Centre",
    editor_contentRight: "Align Right",
    editor_contentJustify: "Justified",
    editor_lineHeight: "Row Height",
    editor_rotate: "Spin",
    editor_diaphaneity: "Transparency",
    editor_round: "Fillet",
    editor_abscissa: "Abscissa",
    editor_ordinate: "Y-axis",
    editor_width: "Width",
    editor_height: "Height",
    editor_animation: "Animation",
    editor_event: "Interaction",
    editor_click_event: "Select Event",
    editor_none: "No Trigger",
    editor_jump: "Jump Scene",
    editor_site: "Third party URL",
    editor_apk: "APK Jump",
    editor_textInfo: "Text Content",
    editor_add_image: "Add Picture",
    editor_del_image: "Delete Picture",
    editor_add_video: "Add Video",
    editor_del_video: "Delete Video",
    editor_select_jump: "Please select the jump scene",
    editor_input_site: "Please enter a third-party URL",
    editor_input_apk: "Please enter the APK package name",
    editor_select_dataSources: "Please select the data source",
    editor_select_scrollDirection: "Please select scroll direction",
    editor_word: "text",
    editor_button_text: "Button Text",
    editor_turn_page: "Interval(S)",
    editor_choose_pdf: "Select PDF",
    editor_flow_address: "Stream Address",
    editor_display_formatter: "Display Format",
    //数据来源选择列表
    edit_none: "None",
    edit_dataSource_company: "Company Name",
    edit_dataSource_status: "Vehicle Status",
    edit_dataSource_number: "License Plate Number",
    edit_dataSource_time: "Driving Duration",
    //滚动方向选择列表
    edit_scrollDirection_left: "Left",
    edit_scrollDirection_right: "Right",
    edit_scrollDirection_up: "UP",
    edit_scrollDirection_down: "Down",
    //切换效果选择列表
    edit_emphasize: "Emphasize",
    edit_animation_bounce: "Bounce",
    edit_animation_flash: "Flashing",
    edit_animation_pulse: "Pulsation",
    edit_animation_rubberBand: "Rubber Band",
    edit_animation_shake: "Shake",
    edit_animation_swing: "Swing",
    edit_animation_tada: "Fluctuation",
    edit_animation_wobble: "Shaking",
    edit_animation_jello: "Jelly",
    edit_spring: "Pop up",
    edit_animation_bounceIn: "Bounce",
    edit_animation_bounceInDown: "Pop down",
    edit_animation_bounceInLeft: "Pop in from left",
    edit_animation_bounceInRight: "Pop in from right",
    edit_animation_bounceInUp: "Pop up",
    edit_fadeIn: "Gradually Appear",
    edit_animation_fadeIn: "Gradually Appear",
    edit_animation_fadeInDown: "Gradually Down",
    edit_animation_fadeInDownBig: "Sharply Downward",
    edit_animation_fadeInLeft: "Emerging from the left",
    edit_animation_fadeInLeftBig: "Gradually appear from the left",
    edit_animation_fadeInRight: "Emerging from the right",
    edit_animation_fadeInRightBig: "Emerging sharply from the right",
    edit_animation_fadeInUp: "Up Gradually",
    edit_animation_fadeInUpBig: "Sharply Upward",
    edit_flip: "Flip",
    edit_animation_flip: "Flip",
    edit_animation_flipInX: "Horizontal Flip In ",
    edit_animation_flipInY: "Vertical Flip In",
    edit_quick: "Fast",
    edit_animation_lightSpeedIn: "Quick Entry",
    edit_slide: "Slide in",
    edit_animation_slideInUp: "Slide up",
    edit_animation_slideInDown: "Slide down",
    edit_animation_slideInLeft: "Swipe in from left",
    edit_animation_slideInRight: "Swipe in from right",
    edit_zoom: "Enlarge",
    edit_reduce: "Narrow",
    edit_animation_zoomIn: "Zoom in",
    edit_animation_zoomInDown: "Zoom down",
    edit_animation_zoomInLeft: "Zoom in from left",
    edit_animation_zoomInRight: "Zoom in from right",
    edit_animation_zoomInUp: "Zoom up",
    editor_text: "Text",
    editor_img: "Picture",
    editor_slide_img: "Carousel",
    editor_video: "Video",
    editor_page: "Web Page",
    editor_weather: "Weather",
    editor_audio: "Music",
    editor_audio_duration: "Music Duration/s",
    editor_time: "Time",
    editor_live: "Live Broadcast",
    editor_live_stream: "Live stream",
    editor_document: "Document",
    editor_button: "Button",
    editor_pdf: "PDF",
    editor_select_template: "Select Template",
    editor_select_image: "Select Picture",
    editor_select_video: "Select Video",
    editor_select_bgAudio: "Select Background Music",
    editor_input_url: "Please enter the web address",
    editor_del_component:
      "Are you sure you want to delete the selected component?",
    editor_del_scene:
      "Are you sure whether you want to delete the current scene?",
    editor_scene_error_message: "At least one scene",
    editor_image_del_error: "At least one picture",
    editor_video_del_error: "At least one video",
    editor_video_and_audio: "Video and audio cannot exist simultaneously",
    editor_video_error_message:
      "Only supports 1 video component or 1 live broadcast component",
    editor_audio_error_message: "Support up to 1 music component",
    editor_weather_error_message: "Support up to 1 weather component",
    editor_message_video: "Support up to 1 music component",
    editor_save_error_message:
      "An error occurred when generating the cover image",
    editor_add_live: "Add Live Broadcast",
    editor_live_address: "Live broadcast address",
    editor_name: "Name",
    editor_week: "Week",
    editor_week_1: "Monday",
    editor_week_2: "Tuesday",
    editor_week_3: "Wednesday",
    editor_week_4: "Thursday",
    editor_week_5: "Friday",
    editor_week_6: "Saturday",
    editor_week_7: "Sunday",
    editor_copy_component: "Copy component",
    editor_delete_component: "Delete component",
    editor_bring_forward: "Move up one level",
    editor_sent_backward: "Move down one level",
    editor_move_top: "Move to the top",
    editor_move_bottom: "Move to the bottom",
    editor_move_top_message: "Moved to the top",
    editor_move_bottom_message: "Moved to the bottom",
    editor_grid_setting: "Grid Setting",
    editor_grid_switch: "Grid Switch",
    editor_grid_width: "Grid Width",
    editor_grid_color: "Grid Color",
    editor_input_live_address: "Please enter the live broadcast address",
    editor_input_page_address: "Please enter the web address",
    editor_input_scene_address: "Please enter the scene name",
    editor_template_message: "Are you sure to use the selected split screen?",
    //编辑器内置国际化移除
    editor_edit_cancel: "Cancel",
    editor_edit_ok: "OK",
    editor_prompt_title: "Reminder",
    editor_op_exit: "Exit",
    editor_page_previous: "Previous Page",
    editor_page_next: "Next Page",
    editor_resolution_ratio: "Resolution",
    editor_custom: "Customization",
    editor_select_scrollSpeed: "Please select a scroll speed",
    common_slow: "Slow",
    common_general: "Moderate",
    common_fast: "Fast",
    common_faster: "Rapid",
    editor_image_error_message: "Supports up to 50 image components",
    editor_slide_image_error_message:
      "Supports up to 5 carousel image components or document components",
    editor_input_address:
      "Unsupported streaming format: please enter the live streaming address in the following formats: rtsp: rtmp: http: https",
    editor_input_width: "Please input the Width",
    editor_input_height: "Please input the Height",
  },
  data: {
    private_agree: `Personal Information Protection and Privacy Policy`,
    user_agree: "Screen Direct User Agreement",
    private_agree_text: private_agree_text,
    user_agree_text: user_agree_text,
  },
  table: {
    actions: "Actions",
    and: "and",
    cancel: "Cancel",
    changeFilterMode: "Change filter mode",
    changeSearchMode: "Change search mode",
    clearFilter: "Clear filter",
    clearSearch: "Clear search",
    clearSort: "Clear sort",
    clickToCopy: "Click to copy",
    collapse: "Collapse",
    collapseAll: "Collapse all",
    columnActions: "Column Actions",
    copiedToClipboard: "Copied to clipboard",
    dropToGroupBy: "Drop to group by {column}",
    edit: "Edit",
    expand: "Expand",
    expandAll: "Expand all",
    filterArrIncludes: "Includes",
    filterArrIncludesAll: "Includes all",
    filterArrIncludesSome: "Includes",
    filterBetween: "Between",
    filterBetweenInclusive: "Between Inclusive",
    filterByColumn: "Filter by {column}",
    filterContains: "Contains",
    filterEmpty: "Empty",
    filterEndsWith: "Ends With",
    filterEquals: "Equals",
    filterEqualsString: "Equals",
    filterFuzzy: "Fuzzy",
    filterGreaterThan: "Greater Than",
    filterGreaterThanOrEqualTo: "Greater Than Or Equal To",
    filterInNumberRange: "Between",
    filterIncludesString: "Contains",
    filterIncludesStringSensitive: "Contains",
    filterLessThan: "Less Than",
    filterLessThanOrEqualTo: "Less Than Or Equal To",
    filterMode: "Filter Mode: {filterType}",
    filterNotEmpty: "Not Empty",
    filterNotEquals: "Not Equals",
    filterStartsWith: "Starts With",
    filterWeakEquals: "Equals",
    filteringByColumn: "Filtering by {column} - {filterType} {filterValue}",
    goToFirstPage: "Go to first page",
    goToLastPage: "Go to last page",
    goToNextPage: "Go to next page",
    goToPreviousPage: "Go to previous page",
    grab: "Grab",
    groupByColumn: "Group by {column}",
    groupedBy: "Grouped by ",
    hideAll: "Hide all",
    hideColumn: "Hide {column} column",
    max: "Max",
    min: "Min",
    move: "Move",
    noRecordsToDisplay: "No records to display",
    noResultsFound: "No results found",
    of: "of",
    or: "or",
    pinToLeft: "Pin to left",
    pinToRight: "Pin to right",
    resetColumnSize: "Reset column size",
    resetOrder: "Reset order",
    rowActions: "Row Actions",
    rowNumber: "#",
    rowNumbers: "Row Numbers",
    rowsPerPage: "Rows per page",
    save: "Save",
    search: "Search",
    selectedCountOfRowCountRowsSelected:
      "{selectedCount} of {rowCount} row(s) selected",
    select: "Select",
    showAll: "Show all",
    showAllColumns: "Show all columns",
    showHideColumns: "Show/Hide columns",
    showHideFilters: "Show/Hide filters",
    showHideSearch: "Show/Hide search",
    sortByColumnAsc: "Sort by {column} ascending",
    sortByColumnDesc: "Sort by {column} descending",
    sortedByColumnAsc: "Sorted by {column} ascending",
    sortedByColumnDesc: "Sorted by {column} descending",
    thenBy: ", then by ",
    toggleDensity: "Toggle density",
    toggleFullScreen: "Toggle full screen",
    toggleSelectAll: "Toggle select all",
    toggleSelectRow: "Toggle select row",
    toggleVisibility: "Toggle visibility",
    ungroupByColumn: "Ungroup by {column}",
    unpin: "Unpin",
    unpinAll: "Unpin all",
    unsorted: "Unsorted",
    // add
    loading_error: "Error loading data",
  },

  dashboard: {
    retail: "Principal",
    region: "Region",
    search: "Search",
  },
  summary: {
    noDataTip: "There is currently no data available.",
    total_digital_signage: "TOTAL DIGITAL SIGNAGE/AREA",
    current_content: "Content resources for the current month",
    signage: "Devices",
    province: "Province",
    p_outlets_installed: "% Outlets Installed",
    outlets_installed: "Outlets Installed",
    outlets_installed2: "Outlets Installed",
    total_outlets: "Total Outlets",
    p_ds_online: " % DS Online",
    ds_online: "DS Online",
    ds_with_note: "DS with Note",
    data_outlet: "Data Outlet",
    total_devices_installed: "Total  Devices Installed",
    new_devices_installed_ytd: "New Devices Installed YTD",
    digital_signage: "Total Digital",
  },
  mapDashboard: {
    details: "Details",
    total: "Total",
    digital_signage: "Digital Signage",
    total_digital_signage: "Total Digital Signage",
    additional_information: "Additional Information",
    digital_signage_usage_duration: "Digital Signage Usage Duration",
    markers_number_tip:
      "The number of Markers represents the number of devices",
  },
  outlet: {
    outlet: "Outlet",
    signage: "Digital Signage",
    total_devices_installed: "Total Devices Installed",
    new_devices_installed: "New Devices Installed",
    year_to_date: "year to date",
    total_outlets_installed: "Total Outlets Installed",
    total_outlets_installed_p: "Rate of installed outlet",
    vs_total_outlets: "VS Total Outlets",
    outlets_installed: "Outlets Installed",
    total_outlets: "Total Outlets",
    new_outlets_installed: "New Outlets Installed",
    new_outlets_installed2: "New Outlets Installed",
    excluding_current_year: "Excluding current year",
    month_to_date: "Month To Date",
  },
  layout: {
    add_new_layout: "Add New Layout",
    width: "Width",
    height: "Height",
    program: "Name",
    resolution: "Resolution",
    input_name: "Please enter the program name",
    input_select_resolution: "Please select a resolution",
    input_number: "please enter a number",
    input_width: "Please enter the width",
    input_height: "Please enter the height",
    input_positive_number: "Please enter a positive number",
    input_integer: "please enter an integer",
    please_select: "Please select Principal type",
    select_retail: "Select a Principal",
    select_auditor: "Select a auditor",
    custom: "custom",
    name_max: "The length of the name must not exceed 50 words",
    name_exit: "Name already exists",
  },
  tenant: {
    tenantId: "Tenant ID",
    infor: "Company Introduction",
    address: "Company Address",
    account: "Account Email",
    enter_account: "Please enter your account (email)",
    enter_info: "Please enter company profile",
    enter_address: "Please enter your company address",
    addTenant: "Add Tenant",
    editTenant: "Edit Tenant",
    enter_address_required: "Company address is mandatory",
  },
  permissions: {
    permission_menu_column_name: "Menu Name",
    permission_menu_column_icon: "Icon",
    permission_menu_column_type: "Type",
    permission_menu_column_sort: "Display Order",
    permission_menu_column_path: "Path",
    permission_menu_column_permi: "Permission Identifier",
    permission_menu_form_name: "Menu Name",
    permission_menu_form_i18n: "Menu Internationalization",
    permission_menu_form_paraent: "Parent Menu",
    permission_menu_form_type: "Menu Type",
    permission_menu_form_path: "Path",
    permission_menu_form_location: "Component Location",
    permission_menu_form_permi: "Permission Identifier",
    permission_menu_form_icon: "Menu Icon",
    permission_menu_form_sort: "Display Order",
    permission_menu_tips_paraent: "Default not filled in as top level",
    permission_menu_title_edit: "Edit Menu",
    permission_menu_title_add: "Add Menu",
    permission_menu_dict_type: "",
    permission_menu_del_desc: "Confirm whether to delete this menu",
    permission_menu_add: "Add Menu",
    permission_menu: "Menu",
    permission_menu_catalogue: "Catalogue",
    permission_menu_btn: "Button",
    permission_menu_api: "Resource",
    permission_menu_name_please: "Please enter menu name",
    permission_menu_i18n_please: "Please enter menu internationalization",
    permission_menu_type_please: "Please select menu type",
    permission_menu_icon_please: "Please select menu icon",
    permission_menu_sort_please: "Please enter sort",
    permission_menu_sort_min: "Sort cannot be less than 0",
    permission_menu_permis_please: "Please enter permission identifier",
    permission_menu_sort_max: "Sort cannot be greater than 9999",
    permission_menu_component_please: "Please enter component location",
    permission_menu_path_please: "Please enter component path",
    user_btn_add: "Add User",
    user_btn_reset: "Reset Password",
    user_btn_enable: "Enable/Disable",
    user_btn_remove: "Delete User",
    user_merchant_select: "Please select a Principal",
    user_column_status: "Status",
    user_column_post: "Position",
    user_column_merchant_name: "Principal Name",
    user_column_role: "Role",
    user_form_role_please: "Please select a role",
    user_form_merchant_tips: "If no Principal is selected, it will be default",
    role_column_key: "Role Identifier",
    role_column_name: "Role Name",
    role_form_name_please: "Please enter the role name",
    role_form_key_please: "Please enter the role identifier",
    role_btn_add: "Add Role",
    role_form_name: "Role Name",
    role_form_key: "Role Identifier",
    role_form_menu_please: "Please select role permissions",
    role_form_menu: "Role Permissions",
    role_form_title_edit: "Edit Role",
    role_form_title_add: "Add Role",
    user_enable_desc:
      "Are you sure you want to enable/disable the current account: {{nameParams}}",
    user_add_title: "Add a user",
    user_edit_title: "Edit user",
  },
};

export default en;
