import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  FormHelperText,
  Ty<PERSON>graphy,
  TextField,
  InputLabel,
  Grid,
  OutlinedInput,
  Stack,
} from "@mui/material";
import { useState, useEffect, useRef } from "react";
import {
  BootstrapA<PERSON>,
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import ZKSelect from "@/components/ZKSelect";
import LoadingButton from "@mui/lab/LoadingButton";
import { useFormik } from "formik";
import { getGroup, getTreeSelect } from "@/service/api/materialGroup";
import * as Yup from "yup";
import { useTranslation } from "react-i18next";
import Treeselect from "@/components/zktreeselect";
import { toast } from "react-toastify";
const CategoryDialog = ({
  currentRowId,
  open,
  onClose,
  onSubmit,
  merchantList,
}) => {
  const { t } = useTranslation();

  const [submitLoading, setSubmitLoading] = useState(false);
  const [groupTree, setGroupTree] = useState([]);
  const treeSelectRef = useRef(null);
  const departmentIdRef = useRef();
  const handleClose = () => {
    categoryForm.handleReset();
    onClose();
  };
  const getTreeData = (value) => {
    getTreeSelect({ departmentId: value }).then((res) => {
      setGroupTree(res.data);
    });
  };
  const handleSubmit = async (values) => {
    setSubmitLoading(true);

    try {
      onSubmit({
        id: currentRowId ? currentRowId : undefined,
        ...values,
      })
        .then(() => {
          setSubmitLoading(false);
          handleClose();
        })
        .catch(() => {
          toast.error(res?.message);
        });
    } finally {
      setSubmitLoading(false);
    }
  };
  const categoryForm = useFormik({
    initialValues: {
      name: "",
      departmentId: "",
      parentId: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handleSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      name: Yup.string()
        .min(1, t("common.common_material_category_name_lenth_rule"))
        .max(50, t("common.common_material_category_name_lenth_rule"))
        .required(t("common.common_material_category_name_please")),
      departmentId: Yup.string().required(t("ips.ips_select_merchant")),
    }),
  });

  useEffect(() => {
    if (open && currentRowId) {
      getFormValue();
    }
  }, [open, currentRowId]);

  const getFormValue = () => {
    getGroup(currentRowId).then((res) => {
      categoryForm.setValues(res?.data);
      if (res?.data?.parentId)
        categoryForm.setFieldValue("parentId", res?.data?.parentId);
      getTreeData(res?.data?.departmentId);
      if (res?.data?.parentId && res?.data?.parentName) {
        treeSelectRef.current.setItem({
          id: res?.data?.parentId,
          name: res?.data?.parentName,
        });
      }

      if (res?.data?.departmentId && res?.data?.departmentName) {
        treeSelectRef.current.setItem({
          id: res?.data?.departmentId,
          name: res?.data?.departmentName,
        });
      }
    });
  };

  return (
    <>
      <BootstrapDialog
        open={open}
        maxWidth={"xs"}
        fullWidth
        onClose={handleClose}>
        <BootstrapDialogTitle onClose={handleClose}>
          <Typography variant="h4" component="p">
            {currentRowId
              ? t("common.common_material_category_edit_title")
              : t("common.common_material_category_add_title")}
          </Typography>
        </BootstrapDialogTitle>
        <form noValidate onSubmit={categoryForm.handleSubmit}>
          <BootstrapContent dividers>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel>
                    {t("common.common_material_category_table_column_merchant")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <ZKSelect
                    id="departmentId"
                    size="small"
                    isClear={!currentRowId}
                    disabled={currentRowId}
                    name="departmentId"
                    value={categoryForm.values.departmentId}
                    options={merchantList}
                    // labelOptions={{
                    //   label: "name",
                    //   value: "id",
                    // }}
                    onClear={() => {
                      categoryForm.setFieldValue("departmentId", "");
                      getTreeData(undefined);
                    }}
                    onBlur={categoryForm.handleBlur}
                    onChange={(e) => {
                      categoryForm.handleChange(e);
                      getTreeData(e.target.value);
                    }}
                    type="text"
                    menuWidth={200}
                    placeholder={t("ips.ips_select_merchant")}
                    error={Boolean(
                      categoryForm.touched.departmentId &&
                        categoryForm.errors.departmentId
                    )}
                  />

                  {categoryForm.touched.departmentId &&
                    categoryForm.errors.departmentId && (
                      <FormHelperText
                        error
                        id="standard-weight-helper-text-categoryForm-departmentId">
                        {categoryForm.errors.departmentId}
                      </FormHelperText>
                    )}
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel>
                    {t("common.common_parent_material_group")}
                    {/* <i style={{ color: "red" }}>*</i> */}
                  </InputLabel>
                  <Treeselect
                    ref={treeSelectRef}
                    data={groupTree}
                    isClear={true}
                    optionValue="id"
                    optionLabel="name"
                    placeholder={t(
                      "common.common_parent_material_group_please"
                    )}
                    onChange={(valuas) => {
                      categoryForm.values.parentId = valuas.id;
                    }}
                    onClear={() => {
                      categoryForm.values.parentId = undefined;
                    }}
                    error={Boolean(
                      categoryForm.touched.parentId &&
                        categoryForm.errors.parentId
                    )}
                    disableParent={true}
                  />
                  {categoryForm.touched.parentId &&
                    categoryForm.errors.parentId && (
                      <FormHelperText
                        error
                        id="standard-weight-helper-text-parentId">
                        {categoryForm.errors.parentId}
                      </FormHelperText>
                    )}
                  <FormHelperText id="standard-weight-helper-text-1">
                    {t("common.common_parent_material_group_tips")}
                  </FormHelperText>
                </Stack>
              </Grid>

              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel>
                    {t("common.common_material_category_name")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <OutlinedInput
                    type="text"
                    fullWidth
                    onBlur={categoryForm.handleBlur}
                    onChange={categoryForm.handleChange}
                    value={categoryForm.values.name}
                    name="name"
                    placeholder={t(
                      "common.common_material_category_name_please"
                    )}
                    error={Boolean(
                      categoryForm.touched.name && categoryForm.errors.name
                    )}
                  />
                  {categoryForm.touched.name && categoryForm.errors.name && (
                    <FormHelperText
                      error
                      id="standard-weight-helper-text-categoryForm-name">
                      {categoryForm.errors.name}
                    </FormHelperText>
                  )}
                </Stack>
              </Grid>
            </Grid>
          </BootstrapContent>
          <BootstrapActions>
            <LoadingButton
              fullWidth
              loading={submitLoading}
              size="large"
              type="submit"
              variant="contained"
              color="primary">
              {t("common.common_edit_save")}
            </LoadingButton>
          </BootstrapActions>
        </form>
      </BootstrapDialog>
    </>
  );
};
export default CategoryDialog;
